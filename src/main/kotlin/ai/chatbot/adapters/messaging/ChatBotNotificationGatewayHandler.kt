package ai.chatbot.adapters.messaging

import ai.chatbot.adapters.billPayment.BillTO
import ai.chatbot.adapters.billPayment.toBillView
import ai.chatbot.adapters.billPayment.toBillViews
import ai.chatbot.app.NotificationService
import ai.chatbot.app.OnePixPayInstrumentation
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InteractionWindowError
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.conversation.UserConversationHistoryNotFound
import ai.chatbot.app.event.EventService
import ai.chatbot.app.event.UserEvent
import ai.chatbot.app.notification.BillComingDueLastWarnDetailsTO
import ai.chatbot.app.notification.BillComingDueRegularDetailsTO
import ai.chatbot.app.notification.BuildNotificationResult
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatBotNotificationGatewayTO
import ai.chatbot.app.notification.ChatbotNotification
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.GenericNotificationDetailsTO
import ai.chatbot.app.notification.KnownNotificationTypes
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.notification.NotificationFormatter
import ai.chatbot.app.notification.NotificationMap
import ai.chatbot.app.notification.NotificationParam
import ai.chatbot.app.notification.OnboardingSinglePixNotificationService
import ai.chatbot.app.notification.OpenFinanceIncentiveDetailsTO
import ai.chatbot.app.notification.OpenFinanceIncentiveType
import ai.chatbot.app.notification.RawTemplateNotificationConfig
import ai.chatbot.app.notification.RegisterCompletedTO
import ai.chatbot.app.notification.TestPixReminderType
import ai.chatbot.app.notification.TestPixreminderDetailsTO
import ai.chatbot.app.notification.WelcomeDetailsTO
import ai.chatbot.app.notification.WelcomeMessageType
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.buildUnregisteredUserAndWallet
import ai.chatbot.app.utils.parseObjectFrom
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.theokanning.openai.completion.chat.ChatMessageRole
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.LocalDateTime
import java.util.Locale
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["test"])
open class ChatBotNotificationGatewayHandler(
    tenantPropagator: TenantPropagator,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationService: NotificationService,
    private val onePixPayInstrumentation: OnePixPayInstrumentation,
    private val buildNotificationService: BuildNotificationService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
    private val onboardingSinglePixNotificationService: OnboardingSinglePixNotificationService,
    private val interactionWindowService: InteractionWindowService,
    private val paymentAdapter: PaymentAdapter,
    private val eventService: EventService,
    private val customNotificationService: CustomNotificationService,
    @Property(name = "aws.sqs.queues.chatBotNotificationGateway") queue: String,
    private val tenantService: TenantService,
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
) : CoroutineAbstractSQSHandler(tenantPropagator, queueName = queue, amazonSQS = amazonSQS, configuration = configuration, consumers = 10) {

    @Trace
    @NewSpan("ChatBotNotificationGatewayHandler#handleMessage")
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val logName = "ChatBotNotificationGatewayHandler#handleMessage"
        val messageBody = m.body()
        val markers = Markers.append("messageBody", messageBody)

        try {
            markers.andAppend("tenantId", tenantService.getTenantName())

            val event = parseObjectFrom<ChatBotNotificationGatewayTO>(m.body())
            markers.andAppend("event", event)
                .andAppend("chatBotMessageType", event.details::class.java.simpleName)

            interactionWindowService.checkAndCreate(event).mapLeft {
                if (it is InteractionWindowError.UserAlreadyHasOpenWindow) {
                    logger.info(markers.andAppend("interactionWindow", it.interactionWindow.toLog()), "$logName/messageQueuedOnInteractionWindow")
                    return SQSHandlerResponse(true)
                }
            }

            val user = event.toUser()
            markers.andAppend("userId", user.id.value)

            val activeConsents = paymentAdapter.getActiveSweepingConsents(user.id, WalletId(event.walletId)).getOrElse {
                emptyList()
            }
            markers.andAppend("activeConsents", activeConsents)

            val currentState = findCurrentState(user, event, activeConsents)

            val wallet = event.toWalletWithBills(activeConsents = activeConsents, currentBills = currentState.walletWithBills.bills)

            val isComingDueNotification = event.details is BillComingDueRegularDetailsTO || event.details is BillComingDueLastWarnDetailsTO
            markers.andAppend("isComingDueNotification", isComingDueNotification)

            if (isComingDueNotification) {
                onePixPayInstrumentation.requestedToNotifyUser(user, wallet.bills)
            }

            val parsedNotifications = buildNotifications(
                event = event,
                wallet = wallet,
                user = user,
                currentState = currentState,
                subscriptionType = event.account.subscriptionType,
            ).getOrElse {
                markers.andAppend("status", it.name)

                when (it) {
                    NotificationError.NO_BILLS_TO_NOTIFY, NotificationError.USER_ALREADY_NOTIFIED, NotificationError.USER_NOT_ELIGIBLE -> {
                        logger.warn(markers, logName)
                    }

                    NotificationError.TRANSACTION_NOT_FOUND, NotificationError.UNKNOWN_TRANSACTION_TYPE -> {
                        logger.error(markers, logName)
                    }
                }

                return SQSHandlerResponse(true)
            }
            markers.andAppend("fakeNotificationMessage", parsedNotifications.map { it.text })
                .andAppend("notifications", parsedNotifications.map { it.notification })

            parsedNotifications.forEach { notification ->
                saveAndNotify(user, notification)
            }

            if (isComingDueNotification) {
                if (parsedNotifications.any { it.notification != null }) {
                    conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(user.id) { stateToUpdate ->
                        stateToUpdate.copy(
                            user = user,
                            internalStateControl = stateToUpdate.internalStateControl.copy(
                                shouldSynchronizeBeforeCompletion = true,
                                billComingDueNotifiedAt = LocalDateTime.now(),
                            ),
                            walletWithBills = event.toWalletWithBills(stateToUpdate.walletWithBills.bills, activeConsents),
                        )
                    }
                }

                onePixPayInstrumentation.notifiedUser(user = user, bills = wallet.bills)
            }

            logger.info(markers, logName)
            return SQSHandlerResponse(true)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    private fun saveAndNotify(user: User, notification: ParsedNotificationTO) {
        val logName = "ChatBotNotificationGatewayHandler#saveAndNotify"
        val markers = Markers.append("userId", user.id.value)
            .andAppend("notification", notification)

        try {
            createAuxiliaryMessage(user, notification)
            if (notification.notification != null) {
                notificationService.notify(notification.notification, notification.delaySeconds)
            }
            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    private fun ChatBotNotificationGatewayTO.toUser(): User {
        return if (account.id.isNotEmpty()) {
            User(
                accountId = AccountId(account.id),
                id = UserId.fromMsisdn(account.msisdn),
                name = account.fullName.split(" ")[0].captilizeFirstLetter(),
                accountGroups = account.accountGroups,
                status = account.status,
                paymentStatus = account.paymentStatus,
            )
        } else {
            buildUnregisteredUserAndWallet(UserId.fromMsisdn(account.msisdn)).user
        }
    }

    private fun findCurrentState(user: User, event: ChatBotNotificationGatewayTO, activeConsents: List<SweepingConsent>): BillComingDueHistoryState {
        return try {
            conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(user.id)
        } catch (e: UserConversationHistoryNotFound) {
            conversationHistoryService.setupEmptyHistoryState(
                userId = user.id,
                initialAssistantMessage = null,
                initialUserAndWallet = UserAndWallet(
                    user = user,
                    wallet = event.toWalletWithBills(
                        activeConsents = activeConsents,
                        currentBills = emptyList(),
                    ),
                ),
            )
        }
    }

    private fun createAuxiliaryMessage(user: User, notification: ParsedNotificationTO) {
        try {
            when (notification.role) {
                ChatMessageRole.ASSISTANT -> conversationHistoryService.createAssistantMessage(user.id, notification.text)
                ChatMessageRole.SYSTEM -> conversationHistoryService.createSystemMessage(user.id, notification.text)
                else -> throw IllegalArgumentException("Parsed notification must be either ASSISTANT or SYSTEM")
            }
        } catch (e: UserConversationHistoryNotFound) {
            throw IllegalStateException("O estado inicial deveria ser garantido no findCurrentState")
        }
    }

    private fun ChatBotNotificationGatewayTO.toWalletWithBills(currentBills: List<BillView>, activeConsents: List<SweepingConsent>): WalletWithBills {
        val (waitingApprovalBills, eventBillsTO) = details.eventBills().partition { it.checkWaitingApproval() }
        val eventBills = eventBillsTO.toBillViews()
        val eventMergeBills = details.eventMergeBills()
        val eventWalletName = details.eventWalletName()

        val activeBills = if (eventMergeBills) {
            val eventBillIds = eventBills.map { it.billId }
            eventBills + (currentBills.filter { it.billId !in eventBillIds })
        } else {
            eventBills
        }

        val orderedBills = activeBills.sortedWith(compareBy<BillView> { it.dueDate }.thenBy { it.billId.value }).mapIndexed { index, billView ->
            val externalBillId = index + 1
            if (billView.externalBillId != externalBillId) {
                billView.copy(externalBillId = externalBillId)
            } else {
                billView
            }
        }

        return WalletWithBills(
            bills = orderedBills,
            totalWaitingApproval = waitingApprovalBills.size,
            walletId = WalletId(walletId),
            walletName = eventWalletName,
            activeConsents = activeConsents,
        )
    }

    private fun buildNotifications(
        event: ChatBotNotificationGatewayTO,
        wallet: WalletWithBills,
        user: User,
        currentState: BillComingDueHistoryState,
        subscriptionType: SubscriptionType,
    ): Either<NotificationError, List<ParsedNotificationTO>> = when (event.details) {
        is BillComingDueLastWarnDetailsTO -> event.details.buildNotifications(user)
        is BillComingDueRegularDetailsTO -> event.details.buildNotifications(user, currentState, subscriptionType)
        is TestPixreminderDetailsTO -> event.details.buildNotifications(user)
        is WelcomeDetailsTO -> event.details.buildNotifications(user)
        is RegisterCompletedTO -> event.details.buildNotifications(user)
        is GenericNotificationDetailsTO -> event.details.buildNotifications(user)
        is OpenFinanceIncentiveDetailsTO -> event.details.buildNotifications(user, currentState, wallet, tenantService.getConfiguration().features.openFinanceIncentive)
    }

    private fun RegisterCompletedTO.buildNotifications(user: User): Either<NotificationError, List<ParsedNotificationTO>> {
        val notification = buildNotificationService.buildRegisterCompletedNotification(
            accountId = user.accountId,
            mobilePhone = user.id.value,
            userName = user.name,
        )

        return notification.let {
            listOf(
                ParsedNotificationTO(
                    text = notificationContextTemplatesService.getRegisterCompletedMessage(user.name),
                    notification = notification,
                ),
            ).right()
        }
    }

    private fun BillComingDueLastWarnDetailsTO.buildNotifications(user: User): Either<NotificationError, List<ParsedNotificationTO>> {
        val paymentLimitTime = bills.first().paymentLimitTime
        val waitingApproveBills = bills.filter { it.checkWaitingApproval() }
        return listOfNotNull(
            ParsedNotificationTO(
                text = notificationContextTemplatesService.getBillComingDueLastWarn(user.name, paymentLimitTime),
                notification = buildNotificationService.buildBillsComingDueLastWarnNotification(
                    accountId = user.accountId,
                    mobilePhone = user.id.value,
                    userName = user.name,
                    paymentLimitTime = paymentLimitTime,
                ),
            ),
            buildWaitingApprovalBillNotification(user, waitingApproveBills.size),
        ).right()
    }

    private fun buildWaitingApprovalBillNotification(user: User, totalWaitingApproveBills: Int): ParsedNotificationTO? {
        if (totalWaitingApproveBills <= 0) {
            return null
        }

        val config = if (totalWaitingApproveBills == 1) {
            KnownTemplateConfigurationKeys.waitingApprovalBillsSingular
        } else {
            KnownTemplateConfigurationKeys.waitingApprovalBillsPlural
        }

        return customNotificationService.buildRawTemplateNotification(
            user = user,
            notificationConfig = RawTemplateNotificationConfig(config, KnownNotificationTypes.WAITING_APPROVAL_BILLS),
            params = listOf(
                NotificationMap(NotificationParam.TOTAL_BILLS, totalWaitingApproveBills.toString()),
            ),
        ).toParsedNotificationTO()
    }

    private fun BillComingDueRegularDetailsTO.buildNotifications(user: User, currentState: BillComingDueHistoryState, subscriptionType: SubscriptionType): Either<NotificationError, List<ParsedNotificationTO>> {
        val logName = "BillComingDueRegularDetailsTO#buildNotifications"
        val markers = Markers.append("user", user)
            .andAppend("currentStateSubscriptionType", currentState.subscription?.type)
            .andAppend("subscriptionType", subscriptionType)

        try {
            /* desativando a notificação para inadimplentes IN_APP,
            * pois está quebrando a ação do template chatbot_ai_notify_bills_coming_due_simple__1_0_0
            * que esta disponível somente para fluxo PIX.
            */
            val isInApp = (currentState.subscription?.type == SubscriptionType.IN_APP) || (subscriptionType == SubscriptionType.IN_APP)
            markers.andAppend("isInApp", isInApp)

            if (user.paymentStatus == AccountPaymentStatus.Overdue && isInApp) {
                logger.warn(markers, logName)
                return NotificationError.USER_NOT_ELIGIBLE.left()
            }

            val regularBills = bills.filter { bill -> !bill.subscriptionFee }
            markers.andAppend("regularBillsSize", regularBills.size)

            if (regularBills.isEmpty()) {
                logger.warn(markers, logName)
                return NotificationError.NO_BILLS_TO_NOTIFY.left()
            }

            val (waitingApproveBillViews, activeBills) = bills.partition { it.checkWaitingApproval() }
            markers.andAppend("waitingApproveBillViewsSize", waitingApproveBillViews.size)
                .andAppend("activeBillsSize", activeBills.size)

            val activeBillViews = activeBills.toBillViews()

            val billComingDueNotification = if (shouldNotifyBillComingDue(activeBills, currentState)) {
                val formattedBills = NotificationFormatter.getFormattedBillInfo(activeBillViews)
                markers.andAppend("formattedBillsSize", formattedBills.size)

                val comingDueNotification = customNotificationService.buildBillsComingDueCustom(user, formattedBills)
                markers.andAppend("comingDueNotification", comingDueNotification)

                ParsedNotificationTO(
                    text = comingDueNotification.historyMessage,
                    notification = comingDueNotification.notification,
                )
            } else {
                null
            }

            val billsChanged = waitingApproveBillViews.size != currentState.walletWithBills.totalWaitingApproval // TODO - poderiamos salvar os BillIds para verificar com mais exatidão
            markers.andAppend("billsChanged", billsChanged)

            val approveBillNotification = if (waitingApproveBillViews.isNotEmpty() && (billsChanged || forceSendNotification(currentState))) {
                buildWaitingApprovalBillNotification(user, waitingApproveBillViews.size)
            } else {
                null
            }
            markers.andAppend("approveBillNotification", approveBillNotification)

            if (billComingDueNotification == null && approveBillNotification == null) {
                logger.warn(markers, logName)
                return NotificationError.USER_ALREADY_NOTIFIED.left()
            }

            logger.info(markers, logName)
            return listOfNotNull(billComingDueNotification, approveBillNotification).right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            throw e
        }
    }

    private fun GenericNotificationDetailsTO.buildNotifications(user: User): Either<NotificationError, List<ParsedNotificationTO>> {
        val templatedNotification = this.notification.toNotification(user.accountId)

        return listOf(
            ParsedNotificationTO(
                notification = templatedNotification,
                text = customNotificationService.getHistoryMessage(templatedNotification),
            ),
        ).right()
    }

    private fun TestPixreminderDetailsTO.buildNotifications(user: User): Either<NotificationError, List<ParsedNotificationTO>> {
        return when (reminderType) {
            TestPixReminderType.NEXT_DAY -> listOf<ParsedNotificationTO>() // TODO gerar notificações
            TestPixReminderType.LAST_DAY -> listOf<ParsedNotificationTO>() // TODO gerar notificações
            TestPixReminderType.EXPIRED -> listOf<ParsedNotificationTO>() // TODO gerar notificações
        }.right()
    }

    private fun WelcomeDetailsTO.buildNotifications(user: User): Either<NotificationError, List<ParsedNotificationTO>> {
        val offerNotification = customNotificationService.buildRawTemplateNotification(
            user = user,
            params = listOf(),
            notificationConfig = RawTemplateNotificationConfig(KnownTemplateConfigurationKeys.onboardingStart, KnownNotificationTypes.ONBOARDING_START),
        )

        return when (type) {
            WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX -> listOf(
                ParsedNotificationTO(
                    role = ChatMessageRole.SYSTEM,
                    text = onboardingSinglePixNotificationService.getSinglePixStartContextMessage(),
                    notification = null,
                ),
                ParsedNotificationTO(
                    text = onboardingSinglePixNotificationService.getSinglePixStartMessage(user.name),
                    notification = buildNotificationService.buildOnboardingSinglePixStartNotification(
                        accountId = user.accountId,
                        mobilePhone = user.id.value,
                        userName = user.name,
                    ),
                    synchronous = true,
                ),
                ParsedNotificationTO(
                    text = offerNotification.historyMessage,
                    notification = offerNotification.notification,
                    delaySeconds = 5,
                    synchronous = true,
                ),
            ).right()
        }
    }

    private fun OpenFinanceIncentiveDetailsTO.buildNotifications(user: User, currentState: BillComingDueHistoryState, wallet: WalletWithBills, incentiveEnabled: Boolean = true): Either<NotificationError, List<ParsedNotificationTO>> {
        fun logAndReturnEmpty(reason: String): Either<NotificationError, List<ParsedNotificationTO>> {
            logger.info(
                Markers.append("userId", user.id.value).andAppend("details", this).andAppend("incentiveEnabled", incentiveEnabled).andAppend("reason", reason),
                "OpenFinanceIncentiveDetails/skip",
            )

            return emptyList<ParsedNotificationTO>().right()
        }

        if (!incentiveEnabled) return logAndReturnEmpty("incentive not enabled")

        if (wallet.hasSweepingAccount) return logAndReturnEmpty("open finance already connected")

        if (currentState.alreadyPromotedSweepingAccount) return logAndReturnEmpty("user already received incentive")

        if (userOptedOut) return logAndReturnEmpty("user opted out of incentive")

        val notifications = when (type) {
            OpenFinanceIncentiveType.DDA -> {
                val billInfo = bill?.let { NotificationFormatter.getFormattedBillInfo(listOf(it.toBillView(0)))[0] } ?: throw IllegalArgumentException("dda incentive without bill")

                listOf(
                    ParsedNotificationTO(
                        text = notificationContextTemplatesService.getPromoteSweepingAccountDDAMessage(billInfo),
                        notification = buildNotificationService.buildPromoteSweepingAccountDDANotification(
                            accountId = user.accountId,
                            mobilePhone = user.id.value,
                            billInfo = billInfo,
                        ),
                        synchronous = true,
                    ),
                )
            }

            OpenFinanceIncentiveType.CASH_IN -> listOf(
                ParsedNotificationTO(
                    text = notificationContextTemplatesService.getPromoteSweepingAccountCashInMessage(),
                    notification = buildNotificationService.buildPromoteSweepingAccountCashInNotification(
                        accountId = user.accountId,
                        mobilePhone = user.id.value,
                    ),
                    synchronous = true,
                ),
            )
        }

        if (notifications.isNotEmpty()) {
            conversationHistoryService.updateConversationHistoryState<BillComingDueHistoryState>(user.id) { state ->
                state.copy(alreadyPromotedSweepingAccount = true)
            }

            val eventSource = when (type) {
                OpenFinanceIncentiveType.DDA -> "dda"
                OpenFinanceIncentiveType.CASH_IN -> "cash_in"
            }
            eventService.send(user.accountId, UserEvent.PROMOTE_SWEEPING_SENT, metadata = mapOf("source" to eventSource))
        }

        return notifications.right()
    }

    private fun shouldNotifyBillComingDue(
        bills: List<BillTO>,
        currentState: BillComingDueHistoryState,
    ): Boolean {
        val conversationHistoryBillIds = currentState.walletWithBills.bills.map { it.billId }.toSet()
        val currentBillIds = bills.map { BillId(it.id) }.toSet()

        val allBillIds = conversationHistoryBillIds.union(currentBillIds)

        val billsChanged = allBillIds.size != bills.size

        return bills.isNotEmpty() && (billsChanged || forceSendNotification(currentState))
    }

    private fun forceSendNotification(currentState: BillComingDueHistoryState) = notifiedHoursAgo(currentState.internalStateControl.billComingDueNotifiedAt, 2)

    private fun notifiedHoursAgo(
        lastNotification: LocalDateTime?,
        hours: Long,
    ) = lastNotification == null || lastNotification.isBefore(LocalDateTime.now().minusHours(hours))

    private fun String.captilizeFirstLetter(): String = this.lowercase().replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("event", m.body()), "ChatBotNotificationGatewayHandler#handleError", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ChatBotNotificationGatewayHandler::class.java)
    }
}

data class ParsedNotificationTO(
    val role: ChatMessageRole = ChatMessageRole.ASSISTANT,
    val text: String,
    val notification: ChatbotNotification?,
    val delaySeconds: Int? = null,
    val synchronous: Boolean = false,
)

enum class NotificationError {
    TRANSACTION_NOT_FOUND, USER_ALREADY_NOTIFIED, UNKNOWN_TRANSACTION_TYPE, NO_BILLS_TO_NOTIFY, USER_NOT_ELIGIBLE
}

private fun BuildNotificationResult.toParsedNotificationTO(): ParsedNotificationTO? {
    if (!this.shouldSend) {
        return null
    }

    return ParsedNotificationTO(
        text = this.historyMessage,
        notification = this.notification,
    )
}
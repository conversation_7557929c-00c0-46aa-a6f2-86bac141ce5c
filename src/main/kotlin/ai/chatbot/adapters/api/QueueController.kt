package ai.chatbot.adapters.api

import ai.chatbot.adapters.messaging.CoroutineAbstractSQSHandler
import ai.chatbot.adapters.notification.MessagePublisher
import ai.chatbot.app.utils.andAppend
import io.micronaut.context.annotation.Property
import io.micronaut.core.async.publisher.Publishers
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Filter
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import net.logstash.logback.marker.Markers
import org.reactivestreams.Publisher
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Filter(value = ["/queue/**"])
class QueueControllerFilter(
    @Property(name = "chatbot-ai-transaction-auth.clientId") private val clientId: String,
    @Property(name = "chatbot-ai-transaction-auth.secret") private val secret: String,
) : HttpServerFilter {
    override fun doFilter(
        request: HttpRequest<*>,
        chain: ServerFilterChain,
    ): Publisher<MutableHttpResponse<*>> {
        if (BasicAuthValidator.validate(request, clientId, secret)) {
            return chain.proceed(request)
        }

        return Publishers.just(HttpResponse.status<Any>(HttpStatus.UNAUTHORIZED))
    }

    @Controller("/queue")
    class QueueController(
        handlers: List<CoroutineAbstractSQSHandler>,
        private val messagePublisher: MessagePublisher,
    ) {
        private val queueNames = handlers.map { it.queueName }

        @Post("/{name}")
        suspend fun message(
            @PathVariable name: String,
            @Body body: Any,
        ): HttpResponse<*>? {
            val logName = "QueueController#message"
            val markers = Markers.append("queueName", name)
                .andAppend("messageBody", body).andAppend("queueNames", queueNames)
            if (!queueNames.contains(name)) {
                logger.warn(markers.andAppend("invalidQueue", name), logName)
                return HttpResponse.notFound("")
            }

            logger.info(markers, logName)

            messagePublisher.sendMessage(name, body)

            return HttpResponse.created("Created")
        }
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(QueueController::class.java)
    }
}
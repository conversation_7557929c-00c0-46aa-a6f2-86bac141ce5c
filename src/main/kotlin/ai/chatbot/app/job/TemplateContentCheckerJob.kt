package ai.chatbot.app.job

import ai.chatbot.app.notification.NotificationValidator
import ai.chatbot.billpayment.app.job.AbstractJob
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Requirements(Requires(notEnv = ["test"]))
@Singleton
open class TemplateContentCheckerJob(
    @Property(name = "schedules.templateContentCheckerJob.cron") cron: String,
    private val notificationValidator: NotificationValidator,
) : AbstractJob(cron = cron) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute() {
        val logName = "TemplateContentCheckerJob#execute"
        try {
            notificationValidator.validateAll()
            logger.info(append("context", "Validação de templates finalizada sem erros."), logName)
        } catch (e: Exception) {
            logger.error(append("context", "Foram encontrados erros na validação dos templates"), logName, e)
        }
    }
}
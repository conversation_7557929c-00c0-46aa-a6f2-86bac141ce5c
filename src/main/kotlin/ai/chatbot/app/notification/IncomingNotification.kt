package ai.chatbot.app.notification

import ai.chatbot.adapters.billPayment.AccountTO
import ai.chatbot.adapters.billPayment.BillTO
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.getObjectMapper
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.readValue
import java.util.*

@JsonIgnoreProperties(ignoreUnknown = true)
data class ChatBotNotificationGatewayTO(
    val walletId: String,
    val account: AccountTO,
    val details: ChatBotNotificationDetailsTO,
)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
sealed interface ChatBotNotificationDetailsTO {
    fun eventBills(): List<BillTO> {
        return emptyList()
    }

    fun eventWalletName(): String? {
        return null
    }

    fun eventMergeBills() = false
}

data class BillComingDueRegularDetailsTO(
    val bills: List<BillTO>,
    val walletName: String,
) : ChatBotNotificationDetailsTO {
    override fun eventBills(): List<BillTO> {
        return bills
    }

    override fun eventWalletName(): String {
        return walletName
    }
}

data class BillComingDueLastWarnDetailsTO(
    val bills: List<BillTO>,
    val walletName: String,
    val hint: String?,
) : ChatBotNotificationDetailsTO {
    override fun eventBills(): List<BillTO> {
        return bills
    }

    override fun eventWalletName(): String {
        return walletName
    }

    override fun eventMergeBills() = true
}

data class WelcomeDetailsTO(
    val type: WelcomeMessageType,
) : ChatBotNotificationDetailsTO

data class TestPixreminderDetailsTO(
    val bills: List<BillTO>,
    val reminderType: TestPixReminderType,
) : ChatBotNotificationDetailsTO {
    override fun eventBills(): List<BillTO> {
        return bills
    }

    override fun eventMergeBills() = true
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class GenericNotificationDetailsTO(
    val notification: GenericNotificationContentTO,
) : ChatBotNotificationDetailsTO

data class OpenFinanceIncentiveDetailsTO(
    val type: OpenFinanceIncentiveType,
    val userOptedOut: Boolean,
    val bank: String? = null,
    val bill: BillTO? = null,
) : ChatBotNotificationDetailsTO

@JsonIgnoreProperties(ignoreUnknown = true)
data class GenericNotificationContentTO(
    val notificationId: String = UUID.randomUUID().toString(),
    val receiver: ReceiverTO,
    val accountId: AccountId? = null,
    val template: NotificationTemplate,
    val configurationKey: String?,
    val parameters: List<String> = listOf(),
    val quickReplyButtonsWhatsAppParameter: List<String> = emptyList(),
    val quickRepliesStartIndex: Int? = null,
    val buttonWhatsAppParameter: ButtonWhatsappParameterTO? = null,
    val media: Map<String, String>? = null,
) {
    fun toNotification(fallbackAccountId: AccountId): ChatbotWhatsappTemplatedNotification {
        return ChatbotWhatsappTemplatedNotification(
            notificationId = notificationId,
            mobilePhone = UserId.fromMsisdn(receiver.msisdn).value,
            accountId = accountId ?: fallbackAccountId,
            template = template,
            configurationKey = configurationKey,
            parameters = parameters,
            quickReplyButtonsWhatsAppParameter = quickReplyButtonsWhatsAppParameter,
            quickRepliesStartIndex = quickRepliesStartIndex,
            buttonWhatsAppParameter = buttonWhatsAppParameter?.let { ButtonWhatsAppRawParameter(it.value) },
            media = media?.parseNotificationMedia(),
        )
    }
}

// FIXME poderia usar o NotificationMediaTO para serializar e descartar essaa desserialização na mão
fun Map<String, String>.parseNotificationMedia(): NotificationMedia {
    val mediaType = this["type"]?.let { NotificationMediaType.valueOf(it) } ?: throw IllegalArgumentException("media does not contain type")

    val objectMapper = getObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
    val mediaStr = objectMapper.writeValueAsString(this)

    return when (mediaType) {
        NotificationMediaType.DOCUMENT -> objectMapper.readValue<NotificationMedia.Document>(mediaStr)
        NotificationMediaType.IMAGE -> objectMapper.readValue<NotificationMedia.Image>(mediaStr)
        NotificationMediaType.VIDEO -> objectMapper.readValue<NotificationMedia.Video>(mediaStr)
    }
}

data class ButtonWhatsappParameterTO(
    val value: String,
)

data class ReceiverTO(
    val msisdn: String,
)

object RegisterCompletedTO : ChatBotNotificationDetailsTO

enum class WelcomeMessageType {
    WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX,
}

enum class TestPixReminderType {
    NEXT_DAY,
    LAST_DAY,
    EXPIRED,
}

enum class OpenFinanceIncentiveType {
    DDA,
    CASH_IN,
}
package ai.chatbot.app.notification

import ai.chatbot.app.bill.BillView
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.event.UserEvent
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.User
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.getObjectMapper
import jakarta.inject.Singleton
import java.time.LocalDate
import java.util.Base64

private const val BLIP_TEMPLATE_LINE_BREAK = " - "

fun String.resolveTemplateVariation(bills: Int): String {
    return when (bills) {
        1 -> this.replace("{{i}}", "singular")
        in 2..BuildNotificationService.MAX_BILLS_IN_NOTIFICATION -> this.replace("{{i}}", "${bills}_bills")
        else -> this.replace("{{i}}", "max")
    }
}

@Singleton
open class BuildNotificationService(
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
    private val tenantService: TenantService,
) {
    fun buildBillsComingDueLastWarnNotification(
        accountId: AccountId,
        mobilePhone: String,
        userName: String,
        paymentLimitTime: String,
        payLoadDate: LocalDate = getLocalDate(),
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueLastWarnEarlyAccess,
            mobilePhone = mobilePhone,
            accountId = accountId,
            arguments = mapOf("USERNAME" to userName, "PAYMENT_LIMIT_TIME" to paymentLimitTime),
            notificationType = KnownNotificationTypes.BILLS_COMING_DUE_LAST_WARN_EARLY_ACCESS,
        )
    }

    fun buildMarkAsPaidAndIgnoreConfirmation(
        user: User,
        formattedBillsMessageToMarkAsPaid: List<String>,
        formattedBillsMessageToIgnore: List<String>,
        payLoadDate: LocalDate = getLocalDate(),
        transactionId: TransactionId,
    ): ChatbotWhatsappSimpleNotification {
        return buildConfirmation(
            user,
            notificationContextTemplatesService.getMarkAsPaidAndIgnoreConfirmation(formattedBillsMessageToMarkAsPaid, formattedBillsMessageToIgnore),
            payLoadDate,
            transactionId,
            InterceptMessagePayloadType.MARK_AS_PAID,
        )
    }

    fun buildMarkAsPaidConfirmation(
        user: User,
        formattedBillsMessage: List<String>,
        payLoadDate: LocalDate = getLocalDate(),
        transactionId: TransactionId,
    ): ChatbotWhatsappSimpleNotification {
        return buildConfirmation(
            user,
            notificationContextTemplatesService.getMarkAsPaidConfirmation(formattedBillsMessage),
            payLoadDate,
            transactionId,
            InterceptMessagePayloadType.MARK_AS_PAID,
        )
    }

    fun buildIgnoreBillsConfirmation(
        user: User,
        formattedBillsMessage: List<String>,
        payLoadDate: LocalDate = getLocalDate(),
        transactionId: TransactionId,
    ): ChatbotWhatsappSimpleNotification {
        return buildConfirmation(
            user,
            notificationContextTemplatesService.getIgnoreBillsConfirmation(formattedBillsMessage),
            payLoadDate,
            transactionId,
            InterceptMessagePayloadType.MARK_AS_PAID,
        )
    }

    private fun buildConfirmation(
        user: User,
        message: String,
        payLoadDate: LocalDate = getLocalDate(),
        transactionId: TransactionId,
        action: InterceptMessagePayloadType,
    ): ChatbotWhatsappSimpleNotification {
        return ChatbotWhatsappSimpleNotification(
            mobilePhone = user.id.value,
            accountId = user.accountId,
            message = message,
            quickReplyButtons = listOf(
                QuickReplyButton(
                    text = "Sim",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = payLoadDate.format(dateFormat),
                            action = action.name,
                            transactionId = transactionId.value,
                        ),
                    ),
                ),
                QuickReplyButton(
                    text = "Não",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = payLoadDate.format(dateFormat),
                            action = InterceptMessagePayloadType.TRANSACTION_CANCEL.name,
                            transactionId = transactionId.value,
                        ),
                    ),
                ),
            ),
        )
    }

    fun buildSufficientBalanceNotification(
        accountId: AccountId,
        mobilePhone: String,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            mobilePhone = mobilePhone,
            accountId = accountId,
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.chatbotSufficientBalance,
            arguments = emptyMap(),
            notificationType = KnownNotificationTypes.SUFFICIENT_BALANCE,
        )
    }

    fun buildOutdatedAction(
        accountId: AccountId,
        mobilePhone: String,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            mobilePhone = mobilePhone,
            accountId = accountId,
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.chatbotOutdatedAction,
            arguments = emptyMap(),
            notificationType = KnownNotificationTypes.CHATBOT_OUTDATED_ACTION,
        )
    }

    fun buildOutdatedPendingBillsNotification(
        user: User,
        formattedBillsMessage: List<String>,
        payLoadDate: LocalDate = getLocalDate(),
    ): ChatbotWhatsappSimpleNotification {
        return ChatbotWhatsappSimpleNotification(
            mobilePhone = user.id.value,
            accountId = user.accountId,
            message = notificationContextTemplatesService.getOutdatedPendingBillsNotificationMessage(formattedBillsMessage),
            quickReplyButtons = if (formattedBillsMessage.size > 1) {
                listOf(
                    QuickReplyButton("Sim, pagar todas", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.SCHEDULE_BILLS.name}"}"""),
                    QuickReplyButton("Pagar algumas", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.PAY_SOME}"}"""),
                    QuickReplyButton("Já paguei", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.MARK_AS_PAID.name}"}"""),
                )
            } else {
                listOf(
                    QuickReplyButton("Sim, pagar este", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.SCHEDULE_BILLS.name}"}"""),
                    QuickReplyButton("Já paguei", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.MARK_AS_PAID.name}"}"""),
                )
            },
        )
    }

    fun buildSweepingAccountBillsScheduleConfirmation(
        user: User,
        formattedBillsMessage: List<String>,
        payLoadDate: LocalDate = getLocalDate(),
        transactionId: TransactionId,
        action: InterceptMessagePayloadType,
    ): ChatbotWhatsappSimpleNotification {
        return buildConfirmation(
            user,
            notificationContextTemplatesService.getSweepingAccountBillsScheduleConfirmation(formattedBillsMessage),
            payLoadDate,
            transactionId,
            action,
        )
    }

    fun buildTokenNotification(
        accountId: AccountId,
        mobilePhone: String,
        token: String,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            mobilePhone = mobilePhone,
            accountId = accountId,
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.tokenMfa,
            arguments = mapOf("TOKEN_MFA" to token),
            notificationType = KnownNotificationTypes.TOKEN_MFA,

        )
    }

    private data class BillsComingDueNotificationTemplateConfig(
        val template: NotificationTemplate,
        val configurationKey: String,
        val parameters: List<String>,
        val quickReplies: List<String>,
    )

    fun buildBillsComingDueUserRequestedNotification(
        user: User,
        formattedBillsMessage: List<String>,
        payLoadDate: LocalDate = getLocalDate(),
    ): List<ChatbotWhatsappSimpleNotification> {
        val (messages, buttons) = if (formattedBillsMessage.isEmpty()) {
            Pair(listOf("Você não possui contas pendentes"), emptyList())
        } else {
            if (formattedBillsMessage.size > 1) {
                Pair(
                    notificationContextTemplatesService.getBillsComingUserRequested(formattedBillsMessage),
                    listOf(
                        QuickReplyButton("Sim, pagar todas", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.SCHEDULE_BILLS.name}"}"""),
                        QuickReplyButton("Pagar algumas", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.PAY_SOME}"}"""),
                        QuickReplyButton("Já paguei", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.MARK_AS_PAID.name}"}"""),
                    ),
                )
            } else {
                Pair(
                    listOf(notificationContextTemplatesService.getBillsComingUserRequestedSingular(formattedBillsMessage)),
                    listOf(
                        QuickReplyButton("Sim, pagar este", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.SCHEDULE_BILLS.name}"}"""),
                        QuickReplyButton("Já paguei", """{"payload": "${payLoadDate.format(dateFormat)}", "action": "${InterceptMessagePayloadType.MARK_AS_PAID.name}"}"""),
                    ),
                )
            }
        }

        return messages.mapIndexed { index, message ->
            ChatbotWhatsappSimpleNotification(
                mobilePhone = user.id.value,
                accountId = user.accountId,
                message = message,
                quickReplyButtons = if (index == messages.lastIndex) {
                    buttons
                } else {
                    null
                },
            )
        }
    }

    fun buildScheduledAmountExceedsSweepingTransactionLimit(
        accountId: AccountId,
        mobilePhone: String,
        transactionId: TransactionId,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.scheduledAmountExceedsSweepingLimit,
            mobilePhone = mobilePhone,
            accountId = accountId,
            arguments = mapOf("TRANSACTION_ID" to transactionId.value),
            notificationType = KnownNotificationTypes.SCHEDULED_AMOUNT_EXCEEDS_SWEEPING_LIMIT,
        )
    }

    fun buildPixAmountExceedsSweepingTransactionLimit(
        accountId: AccountId,
        mobilePhone: String,
        transactionId: TransactionId,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.pixAmountExceedsSweepingLimit,
            mobilePhone = mobilePhone,
            accountId = accountId,
            arguments = mapOf("TRANSACTION_ID" to transactionId.value),
            notificationType = KnownNotificationTypes.PIX_AMOUNT_EXCEEDS_SWEEPING_LIMIT,
        )
    }

    fun buildReminderResponseSuccessNotification(
        accountId: AccountId,
        mobilePhone: String,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            mobilePhone = mobilePhone,
            accountId = accountId,
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.reminderNotificationResponseSuccess,
            arguments = emptyMap(),
            notificationType = KnownNotificationTypes.REMINDER_NOTIFICATION_RESPONSE_SUCCESS,
        )
    }

    fun buildReminderResponseErrorNotification(
        accountId: AccountId,
        mobilePhone: String,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            mobilePhone = mobilePhone,
            accountId = accountId,
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.reminderNotificationResponseError,
            arguments = emptyMap(),
            notificationType = KnownNotificationTypes.REMINDER_NOTIFICATION_RESPONSE_ERROR,
        )
    }

    fun buildAddNewConnectionNotification(
        accountId: AccountId,
        mobilePhone: String,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            mobilePhone = mobilePhone,
            accountId = accountId,
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.utilityAccountAddNewConnection,
            arguments = emptyMap(),
            notificationType = KnownNotificationTypes.UTILITY_ACCOUNT_ADD_NEW_CONNECTION,
        )
    }

    fun buildOnboardingSinglePixStartNotification(
        accountId: AccountId,
        mobilePhone: String,
        userName: String,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.onboardingSinglePixStart,
            mobilePhone = mobilePhone,
            accountId = accountId,
            arguments = mapOf("USERNAME" to userName),
            notificationType = KnownNotificationTypes.ONBOARDING_SINGLE_PIX_START,
        )
    }

    fun buildOnboardingPixConfirmationNotification(
        accountId: AccountId,
        mobilePhone: String,
        userName: String,
        formattedBillsMessage: List<String>,
    ): ChatbotRawTemplatedNotification {
        return ChatbotRawTemplatedNotification(
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.onboardingSinglePixExamplePaymentConfirmation,
            mobilePhone = mobilePhone,
            accountId = accountId,
            arguments = mapOf("USERNAME" to userName, "BILLS_MESSAGE" to formattedBillsMessage.joinToString(BLIP_TEMPLATE_LINE_BREAK)),
            notificationType = KnownNotificationTypes.ONBOARDING_SINGLE_PIX_EXAMPLE_PAYMENT_CONFIRMATION,
        )
    }

    fun buildRegisterCompletedNotification(accountId: AccountId, mobilePhone: String, userName: String) = ChatbotRawTemplatedNotification(
        clientId = tenantService.getConfiguration().clientId,
        mobilePhone = mobilePhone,
        accountId = accountId,
        configurationKey = KnownTemplateConfigurationKeys.registrationCompletion,
        arguments = mapOf("USERNAME" to userName),
        notificationType = KnownNotificationTypes.REGISTRATION_COMPLETION,
    )

    fun buildAuthorizeScheduleBillsNotification(accountId: AccountId, mobilePhone: String, bills: List<BillView>, transactionId: TransactionId): ChatbotRawTemplatedNotification {
        val formattedBillInfo = NotificationFormatter.getFormattedBillInfo(bills).take(10)

        val billParams = formattedBillInfo.mapIndexed { index, bill ->
            if (formattedBillInfo.size > 1) {
                listOf(
                    "BILLS_DESCRIPTION_${index + 1}" to bill.description,
                    "BILLS_AMOUNT_${index + 1}" to bill.amount,
                )
            } else {
                listOf(
                    "BILL_DESCRIPTION" to bill.description,
                    "BILL_AMOUNT" to bill.amount,
                )
            }
        }.flatten().toMap()

        return ChatbotRawTemplatedNotification(
            clientId = tenantService.getConfiguration().clientId,
            configurationKey = KnownTemplateConfigurationKeys.authorizeScheduleBills,
            mobilePhone = mobilePhone,
            accountId = accountId,
            arguments = mapOf(
                "TRANSACTION_ID" to transactionId.value,
                "TEMPLATE_VARIANT" to when (formattedBillInfo.size) {
                    1 -> "singular"
                    in 2..BuildNotificationService.MAX_BILLS_IN_NOTIFICATION -> "${bills}_bills"
                    else -> "max"
                },
            ) + billParams,
        )
    }

    fun buildPromoteSweepingAccountMarkAsPaidNotification(accountId: AccountId, mobilePhone: String) = ChatbotRawTemplatedNotification(
        clientId = tenantService.getConfiguration().clientId,
        configurationKey = KnownTemplateConfigurationKeys.promoteSweepingAccountMarkAsPaid,
        mobilePhone = mobilePhone,
        accountId = accountId,
        arguments = emptyMap(),
        notificationType = KnownNotificationTypes.PROMOTE_SWEEPING_ACCOUNT_MARK_AS_PAID,
    )

    fun buildPromoteSweepingAccountDDANotification(accountId: AccountId, mobilePhone: String, billInfo: FormattedBillInfo) = ChatbotRawTemplatedNotification(
        clientId = tenantService.getConfiguration().clientId,
        configurationKey = KnownTemplateConfigurationKeys.promoteSweepingAccountDDA,
        mobilePhone = mobilePhone,
        accountId = accountId,
        arguments = mapOf("BILL_DESCRIPTION" to billInfo.description, "BILL_AMOUNT" to billInfo.amount),
        notificationType = KnownNotificationTypes.PROMOTE_SWEEPING_ACCOUNT_DDA,
    )

    fun buildPromoteSweepingAccountCashInNotification(accountId: AccountId, mobilePhone: String) = ChatbotRawTemplatedNotification(
        clientId = tenantService.getConfiguration().clientId,
        configurationKey = KnownTemplateConfigurationKeys.promoteSweepingAccountCashIn,
        mobilePhone = mobilePhone,
        accountId = accountId,
        arguments = emptyMap(),
        notificationType = KnownNotificationTypes.PROMOTE_SWEEPING_ACCOUNT_CASH_IN,
    )

    companion object {
        const val MAX_BILLS_IN_NOTIFICATION = 10
    }
}

fun isBlipMessageSizeGreaterThanMax(messageSize: Int): Boolean {
    val maxMessageSize = 1024
    return messageSize > maxMessageSize
}

open class SimpleResponseNotification(
    val userId: String,
    val notificationId: String,
    val message: String?,
    val accountId: String?,
    val buttons: List<QuickReplyButton>? = null,
    val link: CTALink? = null,
    val media: NotificationMedia? = null,
)

data class BlipPayload(
    val payload: String,
    val action: String,
    val transactionId: String? = null,
)

sealed interface ButtonWhatsAppParameter {
    val value: String
}

data class ButtonWhatsAppDeeplinkParameter(val path: String) : ButtonWhatsAppParameter {
    override val value = "app/$path"
}

data class ButtonWhatsAppWebParameter(val path: String) : ButtonWhatsAppParameter {
    override val value = "web/$path"
}

data class ButtonWhatsAppTrackedDeeplinkParameter(val path: String, val event: UserEvent) : ButtonWhatsAppParameter {
    override val value = "app/track/${event.eventName}/${String(encoder.encode("/$path".toByteArray()))}"

    companion object {
        private val encoder = Base64.getUrlEncoder()
    }
}

data class ButtonWhatsAppRawParameter(private val path: String) : ButtonWhatsAppParameter {
    override val value = path
}
package ai.chatbot.app.config

import io.micronaut.context.propagation.slf4j.MdcPropagationContext
import io.micronaut.core.propagation.PropagatedContext
import jakarta.inject.Singleton

const val TENANT_KEY = "X-TENANT-ID"

@Singleton
class TenantPropagator {
    fun <T> executeWithTenant(tenant: String?, block: () -> T): T {
        createScope(mapOf(TENANT_KEY to tenant)).use {
            return block()
        }
    }

    suspend fun <T> executeWithTenantWithSuspend(tenant: String?, block: suspend () -> T): T {
        createScope(mapOf(TENANT_KEY to tenant)).use {
            return block()
        }
    }

    private fun createScope(scopeValues: Map<String, String?>): PropagatedContext.Scope {
        return PropagatedContext.empty()
            .plus(MdcPropagationContext(scopeValues)).propagate()
    }
}
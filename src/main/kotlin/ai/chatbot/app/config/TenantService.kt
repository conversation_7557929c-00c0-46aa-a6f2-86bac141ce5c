package ai.chatbot.app.config

import io.micronaut.context.annotation.Property
import io.micronaut.context.propagation.slf4j.MdcPropagationContext
import io.micronaut.core.propagation.PropagatedContext
import io.micronaut.http.context.ServerHttpRequestContext
import io.micronaut.multitenancy.exceptions.TenantNotFoundException
import io.micronaut.multitenancy.tenantresolver.TenantResolver
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface TenantService {
    fun getConfiguration(): TenantConfiguration
    fun getTenantName(): String
    fun getAllTenantConfigurations(): List<TenantConfiguration>
}

@Singleton
open class TenantServiceImpl(
    private val tenantConfigurations: Map<String, TenantConfiguration>,
    private val tenantResolver: TenantResolver,
    @Property(name = "single-tenant.enabled", defaultValue = "false") private val isSingleTenant: Boolean,
    @Property(name = "single-tenant.tenantId") private val singleTenantId: String?,
) : TenantService {
    private val logger = LoggerFactory.getLogger(TenantService::class.java)

    override fun getConfiguration(): TenantConfiguration {
        val tenantId = resolveTenantId()

        return tenantConfigurations.getOrElse(tenantId.lowercase()) {
            throw IllegalArgumentException("Tenant configuration not found for tenant: $tenantId")
        }
    }

    override fun getAllTenantConfigurations(): List<TenantConfiguration> {
        return tenantConfigurations.values.toList()
    }

    override fun getTenantName(): String = resolveTenantId()

    @Throws(TenantNotFoundException::class)
    private fun resolveTenantId(): String {
        if (isSingleTenant) {
            if (singleTenantId.isNullOrBlank()) {
                throw TenantNotFoundException("Single tenant mode is enabled but no tenant ID is configured")
            }

            return singleTenantId
        }

        if (!PropagatedContext.exists()) {
            logger.error(Markers.append("ACTION", "VERIFY"), "TenantReader")

            // FIXME: Não deve devolver um default futuramente
            return singleTenantId ?: throw TenantNotFoundException("PropagatedContext not found in the context")
        }

        val propagatedContext = PropagatedContext.get()
        val isHttpRequestContextPresent = propagatedContext.find(ServerHttpRequestContext::class.java).isPresent

        if (isHttpRequestContextPresent) {
            return tenantResolver.resolveTenantIdentifier().toString()
        }

        val isMdcPropagationContextPresent = propagatedContext.find(MdcPropagationContext::class.java).isPresent

        if (isMdcPropagationContextPresent) {
            val context = propagatedContext.find(MdcPropagationContext::class.java).get()

            return context.state[TENANT_KEY] ?: throw TenantNotFoundException("Tenant ID not found in the context")
        }

        logger.error(Markers.append("ACTION", "VERIFY"), "TenantReader")

        // FIXME: Não deve devolver um default futuramente
        return singleTenantId ?: throw TenantNotFoundException("HttpServerRequestContext and MdcPropagationContext not found in the context")
    }
}
package ai.chatbot.app.prompt.promptModules.actions

const val addBoletoPrompt = """
[BEGIN - Adi<PERSON> de boleto]
A adição de boletos pode ser feita quando você receber uma linha digitável de 47 ou 48 dígitos ou um código de barras de 44 dígitos, todos numéricos.

-Quando identificar um boleto você deverá chamar a ação [validateBoleto] passando o campo [bill] com a linha digitável ou código de barras informado pelo usuário no formato de lista.
-O usuário pode ou não enviar uma data de vencimento para cada linha digitável ou código de barras.
-O boleto pode estar formatado com espaços, pontos ou traços, mas você deve remover esses caracteres antes de enviar para a ação [validateBoleto].

Exemplos: 
-se receber "34191790010104351004791020150008787820026318" voce deve chamar a ação [validateBoleto] passando o campo [bill] com o valor ["34191790010104351004791020150008787820026318"].
-se receber "84690000002-3 36260162202-4 50615038000-0 00382656646-9" você deve chamar a ação [validateBoleto] passando o campo [bill] com o valor ["846900000023362601622024506150380000003826566469"].
[END - Adição de boleto]
"""
package ai.integration.chatbot

import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.balance
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.subscription
import ai.chatbot.app.user.UserActivity
import ai.chatbot.app.user.UserActivityType
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.WalletWithBills
import arrow.core.right
import io.micronaut.context.ApplicationContext
import io.mockk.coEvery
import jakarta.inject.Named

class PromptValidationIntegrationTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named(FRIDAY_ENV) tenantConfiguration: TenantConfiguration,
) : AbstractIntegrationTest(
    openAIAdapter = openAIAdapter,
    applicationContext = applicationContext,
    printConversation = true,
    tenantConfiguration = tenantConfiguration,
) {
    init {

        beforeEach() {
            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.getSubscription(user.id) } returns subscription.copy(type = SubscriptionType.IN_APP, fee = null).right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
            coEvery { paymentAdapter.getBalanceAndForecast(any(), any()) } returns balance.right()
            coEvery { paymentAdapter.markBillsAsPaid(any(), any(), any()) } returns Unit.right()
            coEvery { paymentAdapter.ignoreBills(any(), any(), any()) } returns Unit.right()
            coEvery { paymentAdapter.getUserActivities(any(), any()) } returns listOf(
                UserActivity(
                    type = UserActivityType.PromotedSweepingAccountOptOut,
                    value = true,
                ),
            ).right()

            val billComingDueState =
                BillComingDueHistoryState(
                    walletWithBills =
                    WalletWithBills(
                        bills = billViews,
                        walletId = walletId,
                        walletName = "walletName",
                    ),
                    balance = null,
                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
                    user = user,
                    contacts = emptyList(),
                    subscription = null,
                )
            historyStateRepository.save(user.id, billComingDueState)
        }

        describe("testes de validação do prompt") {
            describe("quando o usuário mencionar que quer adicionar saldo") {
                it("deve instruir o usuário a fazer um pix para a chave friday") {
                    val userMessage = "Quero adicionar saldo"
                    conversationHistoryService.createUserMessage(user.id, userMessage)
                    baseProcessor.process(user)
                    validateConversation(
                        """
                        Deve instruir o usuário a fazer um pix para o <seu <EMAIL>>.
                    """,
                    )
                }
            }
        }
    }
}
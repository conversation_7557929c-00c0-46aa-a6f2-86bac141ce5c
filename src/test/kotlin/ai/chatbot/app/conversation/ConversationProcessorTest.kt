package ai.chatbot.app.conversation

import ai.chatbot.adapters.api.TransactionPaymentStatus
import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.adapters.repositories.InMemoryHistoryRepository
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TransactionRepository
import ai.chatbot.app.TransactionService
import ai.chatbot.app.bill.PendingBillsService
import ai.chatbot.app.billView
import ai.chatbot.app.config.FeaturesConfig
import ai.chatbot.app.config.TenantConfiguration
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.mockNotificationConfigs
import ai.chatbot.app.newActionExecutorLocator
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.FridayNotificationContextTemplatesService
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.tenantConfiguration
import ai.chatbot.app.transaction.DefaultTransactionService
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.parseObjectFrom
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.core.JsonParseException
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify

class ConversationProcessorTest :
    FunSpec(
        {
            val tenantService = mockk<TenantService>()
            val openAIAdapter: OpenAIAdapter = mockk()
            val notificationService: NotificationService = mockk(relaxed = true)
            lateinit var historyRepository: HistoryRepository
            lateinit var historyStateRepository: HistoryStateRepository
            lateinit var conversationProcessor: ConversationProcessor
            lateinit var conversationHistoryService: ConversationHistoryService
            lateinit var transactionService: TransactionService
            lateinit var paymentAdapter: PaymentAdapter
            lateinit var transactionRepository: TransactionRepository
            lateinit var pendingBillsService: PendingBillsService
            lateinit var actionsManager: ActionsManager
            lateinit var customNotificationService: CustomNotificationService
            val userId = UserId.fromMsisdn("*************")
            val user =
                User(
                    id = userId,
                    accountId = AccountId("accountId"),
                    name = "Nome do usuário",
                    accountGroups = emptyList(),
                    status = AccountStatus.ACTIVE,
                )
            val notificationContextTemplatesService = FridayNotificationContextTemplatesService()

            val mockTenantConfiguration = mockk<TenantConfiguration> {
                every { messages } returns mockNotificationConfigs
                every { appBaseUrl } returns "https://app.base.url"
            }

            beforeEach {
                clearAllMocks()

                every { tenantService.getConfiguration() } returns mockTenantConfiguration
                every { tenantService.getTenantName() } returns "friday"

                historyRepository = InMemoryHistoryRepository()
                historyStateRepository = mockk(relaxed = true)
                paymentAdapter = mockk()
                transactionRepository = mockk()
                pendingBillsService = mockk()

                historyRepository.create(
                    userId = userId,
                    historyStateType = HistoryStateType.BILLS_COMING_DUE,
                    initialUserMessage = null,
                )
                conversationHistoryService =
                    ConversationHistoryService(
                        historyRepository = historyRepository,
                        openAIAdapter = openAIAdapter,
                        paymentAdapter = paymentAdapter,
                        notificationService = mockk(relaxed = true),
                        historyStateRepository = historyStateRepository,
                        pendingBillsService = pendingBillsService,
                        promptService = mockk(relaxed = true),
                    )

                transactionService = DefaultTransactionService(transactionRepository = transactionRepository, paymentAdapter)

                customNotificationService = CustomNotificationService(
                    notificationService = notificationService,
                    conversationHistoryService = conversationHistoryService,
                    templateInfoService = mockk(relaxed = true),
                    tenantService = tenantService,
                    notificationContextTemplatesService = notificationContextTemplatesService,
                )

                actionsManager =
                    spyk(
                        ActionsManager(
                            newActionExecutorLocator(
                                notificationService = notificationService,
                                conversationHistoryService = conversationHistoryService,
                                paymentAdapter = paymentAdapter,
                                transactionService = transactionService,
                                openAIAdapter = openAIAdapter,
                                pendingBillsService = mockk(),
                            ),
                            conversationHistoryService = conversationHistoryService,
                            notificationService = notificationService,
                        ),
                    )

                conversationProcessor =
                    ConversationProcessor(
                        actionsManager = actionsManager,
                        conversationHistoryService = conversationHistoryService,
                        notificationService = notificationService,
                        transactionService = transactionService,
                        onePixPayInstrumentation = mockk(relaxed = true),
                        paymentAdapter = paymentAdapter,
                        buildNotificationService = BuildNotificationService(notificationContextTemplatesService, tenantService),
                        notificationContextTemplatesService = notificationContextTemplatesService,
                        eventService = mockk(relaxed = true),
                        customNotificationService = customNotificationService,
                        tenantService = tenantService,
                    )

                val mockFeatures = mockk<FeaturesConfig>() {
                    every { openFinanceIncentive } returns true
                }

                every { tenantService.getConfiguration() } returns tenantConfiguration().copy(appBaseUrl = "https://app.friday.ai", features = mockFeatures)

                every {
                    openAIAdapter.createChatCompletion(any(), any(), any())
                } returns parseObjectFrom<CompletionMessage>(responseWithOneAction)
            }

            context("quando for uma conversa com o usuário") {
                test("deve retornar apenas uma mensagem do assistente") {
                    conversationHistoryService.createUserMessage(userId, "quero sim")
                    conversationProcessor.process(user)
                    verify {
                        openAIAdapter.createChatCompletion(any(), any(), any())
                        notificationService.notify(any(), any(), any(), any())
                    }
                    historyRepository.findLatest(userId).messages.size shouldBe 2
                }

                context("e tiver mais de uma mensagem pro usuário") {
                    test("deve enviar todas as mensagens do assistente pro usuário em mensagens separadas") {
                        every {
                            openAIAdapter.createChatCompletion(any(), any(), any())
                        } returns parseObjectFrom(responseWithTwoActions)

                        conversationHistoryService.createUserMessage(userId, "quero sim")
                        conversationProcessor.process(user)

                        verify {
                            openAIAdapter.createChatCompletion(any(), any(), any())
                        }

                        verify(exactly = 2) {
                            notificationService.notify(any(), any(), any(), any())
                        }
                    }
                }

                context("quando falhar na geração de mensagem") {
                    test("deve avisar a falha ao ususário") {
                        every {
                            openAIAdapter.createChatCompletion(any(), any(), any())
                        } throws Exception("deu ruim")

                        conversationHistoryService.createUserMessage(userId, "quero sim")
                        conversationProcessor.process(user)

                        verify {
                            openAIAdapter.createChatCompletion(any(), any(), any())
                            notificationService.notify(any(), any(), any(), any())
                        }
                    }
                }

                context("quando gerar um completion com json inválido") {

                    test("deve retentar") {
                        every { openAIAdapter.generateCompletion(any(), any(), any(), any()) } throws JsonParseException(null, "json inválido")
                        conversationHistoryService.createUserMessage(userId, "quero sim")
                        conversationProcessor.process(user)

                        verify(exactly = 1) {
                            openAIAdapter.createChatCompletion(any(), any(), any())
                        }

                        verify(exactly = 1) {
                            openAIAdapter.createChatCompletion(any(), any(), any(), any())
                        }
                    }
                }
            }

            context("quando o usuário confirmar uma transação de pix") {
                test("deve chamar enviar a transação para o paymentAdapter") {
                    val transactionId = TransactionId(value = "TransactionTest")
                    every { historyStateRepository.findLatest(user.id) } returns
                        BillComingDueHistoryState(
                            walletWithBills =
                            WalletWithBills(
                                bills = listOf(billView),
                                walletId = WalletId("walletId"),
                                walletName = "nome da carteira",
                            ),
                            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                            balance = null,
                            user = user,
                            contacts = emptyList(),
                            subscription = null,
                        )
                    every { transactionRepository.save(any()) } returns Unit
                    coEvery { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any()) } returns Unit.right()
                    every { transactionRepository.find(any<TransactionId>()) } returns
                        Transaction(
                            userId = userId,
                            walletId = WalletId(value = "walletId"),
                            id = TransactionId(value = "TransactionTest"),
                            groupId = TransactionGroupId(),
                            status = TransactionStatus.ACTIVE,
                            paymentStatus = TransactionPaymentStatus.UNKNOWN,
                            details =
                            PixTransactionDetails(
                                amount = 2000,
                                pixKey = PixKey(value = "<EMAIL>", type = PixKeyType.EMAIL),
                                recipientName = "Recipient",
                                recipientDocument = "***.456.789-**",
                                recipientInstitution = "Banco teste",
                                sweepingAmount = null,
                                sweepingParticipantId = null,
                                qrCode = null,
                            ),
                        )

                    conversationHistoryService.createUserMessage(userId, "Sim")
                    conversationProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId = transactionId))

                    verify(exactly = 0) {
                        openAIAdapter.createChatCompletion(any(), any(), any())
                    }

                    coVerify { paymentAdapter.pixTransaction(any(), any(), any(), any(), any(), any()) }
                }
            }

            context("quando o usuário rejeitar uma transação de pix") {
                test("deve salvar a transação como CANCELED") {
                    val transactionId = TransactionId(value = "TransactionTest")
                    val transaction =
                        Transaction(
                            userId = userId,
                            walletId = WalletId(value = "walletId"),
                            id = transactionId,
                            groupId = TransactionGroupId(),
                            status = TransactionStatus.ACTIVE,
                            paymentStatus = TransactionPaymentStatus.UNKNOWN,
                            details =
                            PixTransactionDetails(
                                amount = 2000,
                                pixKey = PixKey(value = "<EMAIL>", type = PixKeyType.EMAIL),
                                recipientName = "Recipient",
                                recipientDocument = "***.456.789-**",
                                recipientInstitution = "Banco teste",
                                sweepingAmount = null,
                                sweepingParticipantId = null,
                                qrCode = null,
                            ),
                        )

                    every { historyStateRepository.findLatest(user.id) } returns
                        BillComingDueHistoryState(
                            walletWithBills =
                            WalletWithBills(
                                bills = listOf(billView),
                                walletId = WalletId("walletId"),
                                walletName = "nome da carteira",
                            ),
                            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                            balance = null,
                            user = user,
                            contacts = emptyList(),
                            subscription = null,
                        )
                    every { transactionRepository.save(any()) } returns Unit
                    every { transactionRepository.find(any<TransactionId>()) } returns transaction
                    conversationHistoryService.createUserMessage(userId, "Não")
                    conversationProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transactionId = transactionId))

                    verify(exactly = 0) {
                        openAIAdapter.createChatCompletion(any(), any(), any())
                    }

                    verify(exactly = 1) {
                        transactionRepository.save(transaction.copy(status = TransactionStatus.CANCELED))
                    }
                }
            }

            context("quando o usuário clicar em pagar assinatura") {
                test("deve enviar um código pix com o valor da assinatura") {

                    every { historyStateRepository.findLatest(user.id) } returns
                        BillComingDueHistoryState(
                            walletWithBills =
                            WalletWithBills(
                                bills = listOf(billView),
                                walletId = WalletId("walletId"),
                                walletName = "nome da carteira",
                            ),
                            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                            balance = null,
                            user = user,
                            contacts = emptyList(),
                            subscription = null,
                        )

                    coEvery {
                        paymentAdapter.generatePixQRCode(any(), any(), any(), any())
                    } returns PixQRCode(value = "pixQRcode").right()

                    coEvery {
                        paymentAdapter.getSubscriptionFee(any())
                    } returns 900L.right()

                    conversationHistoryService.createUserMessage(userId, "Pagar assinatura")
                    conversationProcessor.process(user, interceptAction = InterceptAction(InterceptMessagePayloadType.SEND_SUBSCRIPTION_PIX_CODE))

                    val amountSlot = slot<Long>()
                    coVerify {
                        paymentAdapter.generatePixQRCode(userId, capture(amountSlot), WalletId("walletId"), "Pagamento assinatura Friday")
                    }
                    amountSlot.captured shouldBe 900

                    verify {
                        notificationService.notify(userId, user.accountId, "pixQRcode", delay = 1)
                    }
                }
            }

            context("quando o usuário falar alguma coisa que não precisa passar na LLM") {
                context("e a mensagem estiver de acordo com o contexto da ação") {
                    listOf(
                        Pair("Já paguei", InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID)),
                        Pair("Pagar tudo com 1 Pix", InterceptAction(InterceptMessagePayloadType.SEND_PIX_CODE)),
                        Pair("Pagar com 1 Pix", InterceptAction(InterceptMessagePayloadType.SEND_PIX_CODE)),
                        Pair("Pagar assinatura", InterceptAction(InterceptMessagePayloadType.SEND_SUBSCRIPTION_PIX_CODE)),
                        Pair("Não", InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL, transactionId = TransactionId("transactionId"))),
                        Pair("Sim", InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM, transactionId = TransactionId("transactionId"))),
                    ).forEach { message ->
                        test("Não deve chamar a LLM") {
                            every { historyStateRepository.findLatest(user.id) } returns
                                BillComingDueHistoryState(
                                    walletWithBills =
                                    WalletWithBills(
                                        bills = listOf(billView),
                                        walletId = WalletId("walletId"),
                                        walletName = "nome da carteira",
                                    ),
                                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                                    balance = null,
                                    user = user,
                                    contacts = emptyList(),
                                    subscription = null,
                                )

                            conversationHistoryService.createUserMessage(userId, message.first)
                            conversationProcessor.process(user = user, interceptAction = message.second)

                            verify(exactly = 0) {
                                openAIAdapter.createChatCompletion(any(), any(), any())
                            }
                        }
                    }
                }

                context("e a mensagem não estar de acordo com o contexto da ação") {
                    listOf(
                        Pair("Já pagueiii", InterceptAction(InterceptMessagePayloadType.MARK_AS_PAID)),
                        Pair("Pagar tudo Pix", InterceptAction(InterceptMessagePayloadType.SEND_PIX_CODE)),
                        Pair("Pagar Pix", InterceptAction(InterceptMessagePayloadType.SEND_PIX_CODE)),
                        Pair("Pagar minha assinatura", InterceptAction(InterceptMessagePayloadType.SEND_SUBSCRIPTION_PIX_CODE)),
                        Pair("Não", InterceptAction(InterceptMessagePayloadType.TRANSACTION_CONFIRM)),
                        Pair("Sim", InterceptAction(InterceptMessagePayloadType.TRANSACTION_CANCEL)),
                    ).forEach { message ->
                        test("deve interceptar e avisar que estamos com problemas") {
                            every { historyStateRepository.findLatest(user.id) } returns
                                BillComingDueHistoryState(
                                    walletWithBills =
                                    WalletWithBills(
                                        bills = listOf(billView),
                                        walletId = WalletId("walletId"),
                                        walletName = "nome da carteira",
                                    ),
                                    internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
                                    balance = null,
                                    user = user,
                                    contacts = emptyList(),
                                    subscription = null,
                                )

                            conversationHistoryService.createUserMessage(userId, message.first)
                            conversationProcessor.process(user = user, interceptAction = message.second)

                            verify(exactly = 0) {
                                openAIAdapter.createChatCompletion(any(), any(), any())
                                actionsManager.validate(any(), any())
                                actionsManager.execute(any(), any())
                            }

                            verify {
                                notificationService.notify(
                                    any(),
                                    any(),
                                    "Estamos com problemas no momento. Nossa equipe já está atuando para resolver. Tente novamente mais tarde.",
                                )
                            }
                        }
                    }
                }
            }

            context("quando o usuário clicar em tentar novamente uma transferência inteligente iniciada pelo WebApp") {
                val action = InterceptAction(
                    type = InterceptMessagePayloadType.RETRY_SWEEPING,
                    transactionId = null,
                    payload = null,
                    amount = 100,
                )

                val currentWalletId = WalletId("walletId")

                beforeEach {
                    every {
                        historyStateRepository.findLatest(user.id)
                    } returns mockk<BillComingDueHistoryState> {
                        every {
                            walletWithBills
                        } returns mockk {
                            every {
                                walletId
                            } returns currentWalletId
                        }
                    }
                }

                test("deve retornar Intercepted e notificar o usuário em caso de erro genérico") {
                    every {
                        paymentAdapter.retrySweepingTransfer(any(), any(), any(), any())
                    } returns SweepingTransferErrorReason.GenericReason("mocked message").left()

                    val result = conversationProcessor.interceptRetrySweepingTransfer(user = user, action = action)

                    result shouldBe InterceptionResult.INTERCEPTED

                    verify {
                        notificationService.notify(user.id, user.accountId, "mocked message")
                    }
                }

                test("deve retornar Intercepted e notificar o usuário em caso de limite diário excedido") {
                    every {
                        paymentAdapter.retrySweepingTransfer(any(), any(), any(), any())
                    } returns SweepingTransferErrorReason.LimitExceeded(SweepingLimitType.DAILY).left()

                    val result = conversationProcessor.interceptRetrySweepingTransfer(user = user, action = action)

                    result shouldBe InterceptionResult.INTERCEPTED

                    val notificationSlot = slot<ChatbotRawTemplatedNotification>()
                    verify {
                        notificationService.notify(capture(notificationSlot))
                    }
                    with(notificationSlot.captured) {
                        accountId shouldBe user.accountId
                        mobilePhone shouldBe user.id.value
                        configurationKey shouldBe KnownTemplateConfigurationKeys.sweepingDailyLimitExceed
                    }
                }

                SweepingLimitType.values().filter { it != SweepingLimitType.DAILY }.forEach { limit ->
                    test("deve retornar Intercepted e notificar o usuário em caso de limite $limit excedido") {
                        every {
                            paymentAdapter.retrySweepingTransfer(any(), any(), any(), any())
                        } returns SweepingTransferErrorReason.LimitExceeded(limit).left()

                        val result = conversationProcessor.interceptRetrySweepingTransfer(user = user, action = action)

                        result shouldBe InterceptionResult.INTERCEPTED

                        val notificationSlot = slot<ChatbotRawTemplatedNotification>()
                        verify {
                            notificationService.notify(capture(notificationSlot))
                        }
                        with(notificationSlot.captured) {
                            accountId shouldBe user.accountId
                            mobilePhone shouldBe user.id.value
                            configurationKey shouldBe KnownTemplateConfigurationKeys.sweepingLimitExceed
                        }
                    }
                }

                test("deve retornar Intercepted e não notificar o usuário em caso de sucesso") {
                    every {
                        paymentAdapter.retrySweepingTransfer(any(), any(), any(), any())
                    } returns "EndToEnd".right()

                    val result = conversationProcessor.interceptRetrySweepingTransfer(user = user, action = action)

                    result shouldBe InterceptionResult.INTERCEPTED

                    verify {
                        paymentAdapter.retrySweepingTransfer(user.id, currentWalletId, action.amount!!, action.transactionId?.let { SweepingParticipantId(it.value) })
                        val message = "Já estou processando seu pedido. Informarei o resultado em breve."
                        notificationService.notify(userId = user.id, accountId = user.accountId, message)
                    }
                }
            }
        },
    )

const val responseWithOneAction = """
             {
              "verificacaoDeIntegridade": "lorem",
              "entendimento": "ipsum",
              "acoes": [
                {
                  "@type": "sendMessage",
                  "name": "MSG",
                  "content": "Ótimo, usuário"
                }
              ]
             }
        """

const val responseWithTwoActions = """
             {
              "verificacaoDeIntegridade": "lorem",
              "entendimento": "ipsum",
               "acoes": [
                {
                  "@type": "sendMessage",
                  "name": "MSG",
                  "content": "Ótimo, usuário"
                },
                {
                  "@type": "sendMessage",
                  "name": "MSG",
                  "content": "Ótimo, usuário2"
                }
              ]
             }
        """
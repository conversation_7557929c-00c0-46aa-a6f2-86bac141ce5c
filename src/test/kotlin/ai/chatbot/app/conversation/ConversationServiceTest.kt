package ai.chatbot.app.conversation

import ai.chatbot.adapters.waCommCentre.MediaType
import ai.chatbot.adapters.waCommCentre.ProcessedMediaResult
import ai.chatbot.app.NotificationService
import ai.chatbot.app.conversation.ConversationService.Companion.MAX_MESSAGE_SIZE
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class ConversationServiceTest : DescribeSpec({
    val notificationService = mockk<NotificationService>(relaxed = true)
    val conversationHistoryService = mockk<ConversationHistoryService>(relaxed = true)
    val buildNotificationService = mockk<BuildNotificationService>(relaxed = true)
    val notificationContextTemplatesService = mockk<NotificationContextTemplatesService>(relaxed = true)
    val singleMessageProcessor = mockk<SingleChatMessageHandler>(relaxed = true)

    val conversationService = ConversationService(
        singleMessageProcessor = singleMessageProcessor,
        conversationHistoryService = conversationHistoryService,
        notificationService = notificationService,
        buildNotificationService = buildNotificationService,
        notificationContextTemplatesService = notificationContextTemplatesService,
    )

    val userId = UserId("test-user")
    val accountId = AccountId("test-account")
    val user = User(
        id = userId,
        accountId = accountId,
        name = "Test User",
        accountGroups = emptyList(),
        status = AccountStatus.ACTIVE,
    )

    beforeEach {
        clearAllMocks()
        every { conversationHistoryService.createUserMessage(any(), any()) } returns user
    }

    describe("Message processing conditions") {
        describe("when message is too long") {
            it("should truncate message and notify user") {
                val longMessage = "a".repeat(MAX_MESSAGE_SIZE + 100)
                val messageContent = MessageContent(longMessage, null)

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                val text = "Sua mensagem foi muito longa e pode não ter sido processada por completo."

                verify {
                    notificationService.notify(
                        user.id,
                        user.accountId,
                        text,
                    )

                    conversationHistoryService.createAssistantMessage(
                        user.id,
                        text,
                    )
                }
            }
        }

        describe("when message is an emoji") {
            it("process emoji message correctly and RETURN") {
                val emojiMessage = "👍"
                val messageContent = MessageContent(emojiMessage, null)

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                verify {
                    conversationHistoryService.createUserReaction(
                        user.id,
                        emojiMessage,
                    )
                }

                verify(exactly = 0) {
                    notificationService.notify(any(), any(), any(), any())
                }
            }
        }

        describe("when message is a transcript") {
            it("should notify user about transcription") {
                val transcript = "transcribed message"
                val messageContent = MessageContent(
                    "original message",
                    ProcessedMediaResult(mediaType = MediaType.AUDIO, transcription = transcript, qrCodeValues = null),
                )

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                verify {
                    notificationService.notify(
                        user.id,
                        user.accountId,
                        "Transcrição da sua mensagem: $transcript",
                    )

                    conversationHistoryService.createSystemMessage(
                        user.id,
                        "A mensagem anterior foi transcrita do que o usuário enviou",
                    )
                }
            }
        }

        describe("when message contains PIX QR code") {
            it("should process QR code content") {
                val qrCode = "pix code"
                val messageContent = MessageContent(
                    "original message",
                    ProcessedMediaResult(mediaType = MediaType.IMAGE, qrCodeValues = listOf(qrCode)),
                )

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                verify(exactly = 0) {
                    notificationService.notify(any(), any(), any(), any())
                }

                verify {
                    conversationHistoryService.createSystemMessage(
                        user.id,
                        "A mensagem anterior foi o QR extraido do arquivo enviado pelo usuário",
                    )
                }
            }
        }

        describe("when message contains text extracted from the media") {
            it("should process text content") {
                val extractedText = "extracted text from media"
                val messageContent = MessageContent(
                    "original message",
                    ProcessedMediaResult(mediaType = MediaType.IMAGE, textExtraction = extractedText),
                )

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                verify(exactly = 0) {
                    notificationService.notify(any(), any(), any(), any())
                }

                verify {
                    conversationHistoryService.createSystemMessage(
                        user.id,
                        "A mensagem anterior foi o texto extraído de uma imagem enviada pelo usuário que pode conter uma chave PIX",
                    )
                }
            }
        }

        describe("when no QR code or Barcode is found in the media file") {
            it("if there is text in the media, should create history entry and should not notify about missing qrcode or barcode") {
                val messageContent = MessageContent(
                    "original message",
                    ProcessedMediaResult(mediaType = MediaType.IMAGE, qrCodeValues = emptyList(), boletoInfo = emptyList(), textExtraction = "Some text in the image"),
                )

                val text = "Não consegui identificar um PIX ou código de barras na imagem que você enviou. Consegue enviar outra?"

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                verify(exactly = 0) {
                    notificationService.notify(
                        user.id,
                        user.accountId,
                        text,
                    )
                    conversationHistoryService.createAssistantMessage(
                        user.id,
                        text,
                    )
                }

                verify { conversationHistoryService.createSystemMessage(user.id, "A mensagem anterior foi o texto extraído de uma imagem enviada pelo usuário que pode conter uma chave PIX") }

                verify(exactly = 1) {
                    singleMessageProcessor.handle(any(), any())
                }
            }

            it("should notify user about missing code in the image and RETURN") {
                val messageContent = MessageContent(
                    "original message",
                    ProcessedMediaResult(mediaType = MediaType.IMAGE, qrCodeValues = emptyList(), boletoInfo = emptyList()),
                )

                val text = "Não consegui identificar um PIX ou código de barras na imagem que você enviou. Consegue enviar outra?"

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                verify {
                    notificationService.notify(
                        user.id,
                        user.accountId,
                        text,
                    )
                    conversationHistoryService.createAssistantMessage(
                        user.id,
                        text,
                    )
                }

                verify(exactly = 0) {
                    singleMessageProcessor.handle(any(), any())
                }
            }

            it("should not notify when QR code is found but no barcode") {
                val messageContent = MessageContent(
                    "original message",
                    ProcessedMediaResult(
                        mediaType = MediaType.IMAGE,
                        qrCodeValues = listOf("00020126580014BR.GOV.BCB.PIX013634377405-c606-4da0-b944-00503596d12c520400005303986540525.505802BR5924Marlon da Costa Moncores6009SAO PAULO61080540900062140510testepixqr63044A81"),
                        boletoInfo = emptyList(),
                    ),
                )

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                verify(exactly = 0) {
                    notificationService.notify(any(), any(), any(), any())
                }

                verify {
                    conversationHistoryService.createSystemMessage(
                        user.id,
                        "A mensagem anterior foi o QR extraido do arquivo enviado pelo usuário",
                    )
                }
            }

            it("should not notify when barcode is found but no QR code") {
                val messageContent = MessageContent(
                    "original message",
                    ProcessedMediaResult(
                        mediaType = MediaType.DOCUMENT,
                        qrCodeValues = emptyList(),
                        boletoInfo = listOf(BoletoInfo("23790123456000078901234567890123123456789000012", "2025-04-03")),
                    ),
                )

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                verify(exactly = 0) {
                    notificationService.notify(any(), any(), any(), any())
                }

                verify {
                    conversationHistoryService.createSystemMessage(
                        user.id,
                        "A mensagem anterior foi o código de barras e vencimento do boleto extraídos do arquivo enviado pelo usuário",
                    )
                }
            }

            it("should notify user about missing code in the document and RETURN") {
                val messageContent = MessageContent(
                    "original message",
                    ProcessedMediaResult(mediaType = MediaType.DOCUMENT, qrCodeValues = emptyList(), boletoInfo = emptyList()),
                )

                val text = "Não consegui identificar um PIX ou código de barras no documento que você enviou. Consegue enviar outro?"

                conversationService.asyncProcessChat(
                    user.id,
                    messageContent,
                    UserMessagePayloadValidationResult.VALID,
                )

                verify {
                    notificationService.notify(
                        user.id,
                        user.accountId,
                        text,
                    )
                    conversationHistoryService.createAssistantMessage(
                        user.id,
                        text,
                    )
                }

                verify(exactly = 0) {
                    singleMessageProcessor.handle(any(), any())
                }
            }
        }
    }

    describe("Message content selection") {
        it("should use transcription when available") {
            val transcription = "transcribed text"
            val qrCode = "pix code"
            val barcode = BoletoInfo("123456", "2024-12-31")
            val userText = "user text"

            val messageContent = MessageContent(
                userText,
                ProcessedMediaResult(
                    mediaType = MediaType.AUDIO,
                    transcription = transcription,
                    qrCodeValues = listOf(qrCode),
                    boletoInfo = listOf(barcode),
                ),
            )

            messageContent.content shouldBe transcription
        }

        it("should use QR code when transcription is null") {
            val qrCode = "pix code"
            val barcode = BoletoInfo("123456", "2024-12-31")
            val userText = "user text"

            val messageContent = MessageContent(
                userText,
                ProcessedMediaResult(
                    mediaType = MediaType.IMAGE,
                    transcription = null,
                    qrCodeValues = listOf(qrCode),
                    boletoInfo = listOf(barcode),
                ),
            )

            messageContent.content shouldBe qrCode
        }

        it("should use barcode when transcription and QR code are null") {
            val barcode = BoletoInfo("123456", "2024-12-31")
            val userText = "user text"

            val messageContent = MessageContent(
                userText,
                ProcessedMediaResult(
                    mediaType = MediaType.DOCUMENT,
                    transcription = null,
                    qrCodeValues = emptyList(),
                    boletoInfo = listOf(barcode),
                ),
            )

            messageContent.content shouldBe "123456\n\nVencimento: 2024-12-31"
        }

        it("should use user text when no media content is available") {
            val userText = "user text"

            val messageContent = MessageContent(
                userText,
                ProcessedMediaResult(
                    mediaType = MediaType.IMAGE,
                    transcription = null,
                    qrCodeValues = emptyList(),
                    boletoInfo = emptyList(),
                ),
            )

            messageContent.content shouldBe userText
        }

        it("should use user text when processedMediaResult is null") {
            val userText = "user text"

            val messageContent = MessageContent(
                userText,
                null,
            )

            messageContent.content shouldBe userText
        }
    }
})
package ai.chatbot.app.notification

import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.bill.BillId
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.conversation.InterceptMessagePayloadType
import ai.chatbot.app.defaultTenantService
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountPaymentStatus
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.withGivenDateTime
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.maps.shouldContainExactly
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.util.UUID

@MicronautTest(environments = [FRIDAY_ENV])
class BuildNotificationServiceTest() : DescribeSpec() {
    private val tenantService = defaultTenantService()
    private val templateInfoService = mockk<TemplateInfoService>()

    private val notificationContextTemplatesService = mockk<NotificationContextTemplatesService>()
    private val buildNotificationService = BuildNotificationService(notificationContextTemplatesService, tenantService = tenantService)
    private val customNotificationService = CustomNotificationService(
        notificationService = mockk(),
        conversationHistoryService = mockk(),
        templateInfoService = templateInfoService,
        tenantService = defaultTenantService(),
        notificationContextTemplatesService = notificationContextTemplatesService,
    )

    private val blipTemplateLineBreak = " - "
    private val billViews = listOf(billView, billView2, billView3)
    private val accountId = AccountId(value = "ACCOUNT-ID")
    private val userId = UserId("*************")
    private val mobilePhone = "***********"
    private val user =
        User(
            accountId = accountId,
            id = userId,
            name = "Nome",
            accountGroups = emptyList(),
            paymentStatus = AccountPaymentStatus.UpToDate,
            status = AccountStatus.ACTIVE,
        )

    init {

        beforeEach() {
            every {
                templateInfoService.getHistoryMessage(any())
            } returns "Conteudo default da mensagem"
        }

        describe("quando chamar buildBillsComingDueLastWarnNotification") {
            val currentDate = BrazilZonedDateTimeSupplier.getZonedDateTime()
            val paymentLimitTime = "paymentLimitTime"

            it("deve gerar a notificacao quando") {
                withGivenDateTime(currentDate) {
                    val result =
                        buildNotificationService.buildBillsComingDueLastWarnNotification(
                            accountId = accountId,
                            mobilePhone = mobilePhone,
                            userName = user.name,
                            paymentLimitTime = paymentLimitTime,
                            payLoadDate = currentDate.toLocalDate(),
                        )

                    result.configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueLastWarnEarlyAccess
                    result.mobilePhone shouldBe mobilePhone
                    result.accountId shouldBe accountId
                    result.notificationType shouldBe KnownNotificationTypes.BILLS_COMING_DUE_LAST_WARN_EARLY_ACCESS
                    result.clientId shouldBe tenantService.getConfiguration().clientId
                    result.arguments shouldContainExactly mapOf("USERNAME" to user.name, "PAYMENT_LIMIT_TIME" to paymentLimitTime)
                }
            }
        }

        describe("quando chamar buildMarkAsPaidAndIgnoreConfirmation") {
            it("deve gerar a notificacao") {
                val formattedBillsMessageToMarkAsPaid = NotificationFormatter.buildBillNotificationMessage(listOf(billView, billView2))
                val formattedBillsMessageToIgnore = NotificationFormatter.buildBillNotificationMessage(listOf(billView3))

                every { notificationContextTemplatesService.getMarkAsPaidAndIgnoreConfirmation(any(), any()) } returns "Mensagem de confirmação"

                val result =
                    buildNotificationService.buildMarkAsPaidAndIgnoreConfirmation(
                        formattedBillsMessageToMarkAsPaid = formattedBillsMessageToMarkAsPaid,
                        formattedBillsMessageToIgnore = formattedBillsMessageToIgnore,
                        transactionId = TransactionId("id"),
                        user = user,
                    )

                result.message shouldBe "Mensagem de confirmação"
                result.quickReplyButtons!!.map { it.text } shouldBe listOf("Sim", "Não")
                result.quickReplyButtons!!.map { it.payload } shouldContainExactlyInAnyOrder listOf(
                    getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.MARK_AS_PAID.name,
                            transactionId = "id",
                        ),
                    ),
                    getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.TRANSACTION_CANCEL.name,
                            transactionId = "id",
                        ),
                    ),
                )
            }
        }

        describe("quando chamar buildMarkAsPaidConfirmation") {
            it("deve gerar a notificacao") {
                val formattedBillsMessage = NotificationFormatter.buildBillNotificationMessage(billViews)

                every { notificationContextTemplatesService.getMarkAsPaidConfirmation(any()) } returns "Mensagem de confirmação"

                val result =
                    buildNotificationService.buildMarkAsPaidConfirmation(
                        formattedBillsMessage = formattedBillsMessage,
                        transactionId = TransactionId("id"),
                        user = user,
                    )

                result.message shouldBe "Mensagem de confirmação"
                result.quickReplyButtons!!.map { it.text } shouldBe listOf("Sim", "Não")
                result.quickReplyButtons!!.map { it.payload } shouldContainExactlyInAnyOrder listOf(
                    getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.MARK_AS_PAID.name,
                            transactionId = "id",
                        ),
                    ),
                    getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.TRANSACTION_CANCEL.name,
                            transactionId = "id",
                        ),
                    ),
                )
            }
        }

        describe("quando chamar buildIgnoreBillsConfirmation") {
            it("deve gerar a notificacao") {
                val formattedBillsMessage = NotificationFormatter.buildBillNotificationMessage(billViews)

                every { notificationContextTemplatesService.getIgnoreBillsConfirmation(any()) } returns "Mensagem de confirmação"

                val result =
                    buildNotificationService.buildIgnoreBillsConfirmation(
                        formattedBillsMessage = formattedBillsMessage,
                        transactionId = TransactionId("id"),
                        user = user,
                    )

                result.message shouldBe "Mensagem de confirmação"
                result.quickReplyButtons!!.map { it.text } shouldBe listOf("Sim", "Não")
                result.quickReplyButtons!!.map { it.payload } shouldContainExactlyInAnyOrder listOf(
                    getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.MARK_AS_PAID.name,
                            transactionId = "id",
                        ),
                    ),
                    getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.TRANSACTION_CANCEL.name,
                            transactionId = "id",
                        ),
                    ),
                )
            }
        }

        describe("quando chamar buildSufficientBalanceNotification") {
            it("deve gerar a notificação") {
                val result =
                    buildNotificationService.buildSufficientBalanceNotification(
                        accountId = accountId,
                        mobilePhone = mobilePhone,
                    )

                result.configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotSufficientBalance
                result.mobilePhone shouldBe mobilePhone
                result.accountId shouldBe accountId
                result.arguments shouldBe emptyMap()
            }
        }

        describe("quando chamar buildOutdatedAction") {
            it("deve gerar a notificação") {
                val result =
                    buildNotificationService.buildOutdatedAction(
                        accountId = accountId,
                        mobilePhone = mobilePhone,
                    )

                result.configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotOutdatedAction
                result.mobilePhone shouldBe mobilePhone
                result.accountId shouldBe accountId
                result.arguments shouldBe emptyMap()
            }
        }

        describe("quando chamar buildBillsComingDueCustom") {
            every {
                notificationContextTemplatesService.getBillsComingDue(any(), any())
            } returns "fake"

            it("deve gerar a notificação quando paymentStatus for igual a Overdue") {
                val formattedBillsMessage = NotificationFormatter.getFormattedBillInfo(billViews)
                val userLocal = user.copy(paymentStatus = AccountPaymentStatus.Overdue)

                val result =
                    customNotificationService.buildBillsComingDueCustom(
                        user = userLocal,
                        bills = formattedBillsMessage,
                    )

                with(result.notification) {
                    shouldBeTypeOf<ChatbotRawTemplatedNotification>()
                    configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueSimple
                    arguments shouldBe
                        mapOf(
                            "USER_NAME" to user.name,
                        )
                }
            }
            it("deve gerar a notificação quando usuário tiver múltiplas contas") {
                val formattedBillsMessage = NotificationFormatter.getFormattedBillInfo(billViews)
                val currentDate = BrazilZonedDateTimeSupplier.getZonedDateTime()

                withGivenDateTime(currentDate) {
                    val result =
                        customNotificationService.buildBillsComingDueCustom(
                            user = user,
                            bills = formattedBillsMessage,
                        )

                    with(result.notification) {
                        shouldBeTypeOf<ChatbotRawTemplatedNotification>()
                        configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDue
                        arguments shouldContainExactly mapOf(
                            "USER_NAME" to user.name,
                            "TEMPLATE_VARIANT" to "3_bills",
                        ) + formattedBillsMessage.mapIndexed { index, bill ->
                            listOf(
                                "BILL_DESCRIPTION_${index + 1}" to bill.description,
                                "AMOUNT_${index + 1}" to bill.amount,
                            )
                        }.flatten().toSet()
                    }
                }
            }

            it("deve gerar a notificação quando possuir apenas 1 conta") {
                val billList = listOf(billView)
                val formattedBillsMessage = NotificationFormatter.getFormattedBillInfo(billList)
                val currentDate = BrazilZonedDateTimeSupplier.getZonedDateTime()

                withGivenDateTime(currentDate) {
                    val result =
                        customNotificationService.buildBillsComingDueCustom(
                            user = user,
                            bills = formattedBillsMessage,
                        )

                    with(result.notification) {
                        shouldBeTypeOf<ChatbotRawTemplatedNotification>()
                        configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueSingular
                        arguments shouldContainExactly mapOf(
                            "USER_NAME" to user.name,
                            "BILL_DESCRIPTION" to formattedBillsMessage.single().description,
                            "AMOUNT" to formattedBillsMessage.single().amount,
                        )
                    }
                }
            }
            it("deve gerar a notificação quando usuário tiver mais de 10 contas contendo apenas 10 contas") {
                val bills = listOf(
                    billView,
                    billView2,
                    billView3,
                    billView3.copy(billId = BillId(UUID.randomUUID().toString()), externalBillId = 4),
                    billView3.copy(billId = BillId(UUID.randomUUID().toString()), externalBillId = 5),
                    billView3.copy(billId = BillId(UUID.randomUUID().toString()), externalBillId = 6),
                    billView3.copy(billId = BillId(UUID.randomUUID().toString()), externalBillId = 7),
                    billView3.copy(billId = BillId(UUID.randomUUID().toString()), externalBillId = 8),
                    billView3.copy(billId = BillId(UUID.randomUUID().toString()), externalBillId = 9),
                    billView3.copy(billId = BillId(UUID.randomUUID().toString()), externalBillId = 10),
                    billView3.copy(billId = BillId(UUID.randomUUID().toString()), externalBillId = 11),
                )

                val formattedBillsMessage = NotificationFormatter.getFormattedBillInfo(bills)
                val currentDate = BrazilZonedDateTimeSupplier.getZonedDateTime()

                withGivenDateTime(currentDate) {
                    val result =
                        customNotificationService.buildBillsComingDueCustom(
                            user = user,
                            bills = formattedBillsMessage,
                        )

                    with(result.notification) {
                        shouldBeTypeOf<ChatbotRawTemplatedNotification>()
                        configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueMax
                        arguments shouldContainExactly mapOf(
                            "USER_NAME" to user.name,
                        ) + formattedBillsMessage.take(10).mapIndexed { index, bill ->
                            listOf(
                                "BILL_DESCRIPTION_${index + 1}" to bill.description,
                                "AMOUNT_${index + 1}" to bill.amount,
                            )
                        }.flatten().toSet()
                    }
                }
            }

            it("deve gerar a notificação quando a mensagem exceder o tamanho de 1024 caracteres") {
                every {
                    notificationContextTemplatesService.getBillsComingDue(any(), any())
                } returns "a".repeat(1025)

                val billList = listOf(billView)
                val formattedBillsMessage = NotificationFormatter.getFormattedBillInfo(billList)
                val currentDate = BrazilZonedDateTimeSupplier.getZonedDateTime()

                withGivenDateTime(currentDate) {
                    val result =
                        customNotificationService.buildBillsComingDueCustom(
                            user = user,
                            bills = formattedBillsMessage,
                        )

                    with(result.notification) {
                        shouldBeTypeOf<ChatbotRawTemplatedNotification>()
                        configurationKey shouldBe KnownTemplateConfigurationKeys.chatbotAiNotifyBillsComingDueBasic
                        arguments shouldContainExactly mapOf(
                            "USER_NAME" to user.name,
                            "TOTAL_BILLS" to "1",
                        )
                    }
                }
            }
        }

        describe("quando chamar buildReminderResponseSuccessNotification") {
            it("deve gerar a notificação") {
                val result =
                    buildNotificationService.buildReminderResponseSuccessNotification(
                        accountId = accountId,
                        mobilePhone = mobilePhone,
                    )

                result.configurationKey shouldBe KnownTemplateConfigurationKeys.reminderNotificationResponseSuccess
                result.arguments shouldBe emptyMap()
            }
        }

        describe("quando chamar buildReminderResponseErrorNotification") {
            it("deve gerar a notificação") {
                val result =
                    buildNotificationService.buildReminderResponseErrorNotification(
                        accountId = accountId,
                        mobilePhone = mobilePhone,
                    )

                result.configurationKey shouldBe KnownTemplateConfigurationKeys.reminderNotificationResponseError
                result.arguments shouldBe emptyMap()
            }
        }

        describe("quando chamar buildAddNewConnectionNotification") {
            it("deve gerar a notificação") {
                val result =
                    buildNotificationService.buildAddNewConnectionNotification(
                        accountId = accountId,
                        mobilePhone = mobilePhone,
                    )

                result.configurationKey shouldBe KnownTemplateConfigurationKeys.utilityAccountAddNewConnection
                result.arguments shouldBe emptyMap()
            }
        }
    }
}
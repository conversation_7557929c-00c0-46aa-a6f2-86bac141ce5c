package ai.chatbot.app.notification

import ai.chatbot.app.defaultTenantService
import io.kotest.assertions.throwables.shouldNotThrow
import io.kotest.core.spec.style.DescribeSpec
import io.mockk.mockk

class NotificationValidatorTest : DescribeSpec() {
    private val tenantService = defaultTenantService()
    private val notificationContext = mockk<NotificationContextTemplatesService>()
    private val buildNotificationService = BuildNotificationService(notificationContext, tenantService)
    private val notificationValidator = NotificationValidator(
        buildNotificationService = buildNotificationService,
        waCommCentreAdapter = mockk(relaxed = true),
    )
    init {
        describe("quando ativar o validator") {
            it("deve rodar a validação para todos os métodos que montam tipos de ChatbotNotification") {
                notificationValidator.validateAll()
                shouldNotThrow<IllegalStateException> { IllegalStateException() }
            }
        }
    }
}
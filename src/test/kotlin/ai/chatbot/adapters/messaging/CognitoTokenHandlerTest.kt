package ai.chatbot.adapters.messaging

import ai.chatbot.app.NotificationService
import ai.chatbot.app.config.TenantPropagator
import ai.chatbot.app.encrypt.EncryptService
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.getObjectMapper
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import software.amazon.awssdk.services.sqs.model.Message

class CognitoTokenHandlerTest : DescribeSpec() {

    private val notificationService: NotificationService = mockk(relaxed = true)
    private val encryptService: EncryptService = mockk(relaxed = true)
    private val buildNotificationService: BuildNotificationService = mockk(relaxed = true)
    private val tenantPropagator: TenantPropagator = mockk(relaxed = true)

    private lateinit var handler: CognitoTokenHandler

    init {
        beforeEach {
            clearAllMocks()

            handler = CognitoTokenHandler(
                tenantPropagator = tenantPropagator,
                queue = "test-cognito-token-queue",
                amazonSQS = mockk(relaxed = true),
                configuration = SQSMessageHandlerConfiguration(
                    sqsWaitTime = 0,
                    sqsCoolDownTime = 0,
                    visibilityTimeout = 0,
                    dlqEnabled = false,
                    maxNumberOfMessages = 0,
                ),
                notificationService = notificationService,
                encryptService = encryptService,
                buildNotificationService = buildNotificationService,
            )
        }

        describe("quando receber uma mensagem de token do Cognito") {
            context("com código já descriptografado") {
                it("deve processar a mensagem e enviar notificação sem descriptografar") {
                    // Given
                    val decryptedToken = "123456"
                    val phoneNumber = "+*************"
                    val accountId = "ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"

                    val cognitoTokenMessage = CognitoTokenMessageTO(
                        code = null,
                        decryptedCode = decryptedToken,
                        source = "cognito",
                        username = "testuser",
                        sub = "sub-123",
                        accountId = accountId,
                        userPoolId = "us-east-1_ABC123",
                        phoneNumber = phoneNumber,
                        phoneNumberVerified = true,
                        email = "<EMAIL>",
                        emailVerified = true,
                        userStatus = "CONFIRMED",
                        timestamp = "2023-01-01T00:00:00Z",
                    )

                    val expectedNotification = ChatbotRawTemplatedNotification(
                        mobilePhone = phoneNumber,
                        accountId = AccountId(accountId),
                        clientId = mockk(),
                        configurationKey = mockk(),
                    )

                    every {
                        buildNotificationService.buildTokenNotification(
                            accountId = AccountId(accountId),
                            mobilePhone = phoneNumber,
                            token = decryptedToken,
                        )
                    } returns expectedNotification

                    val message = buildMessage(cognitoTokenMessage)

                    // When
                    val response = handler.handleMessage(message)

                    // Then
                    response.shouldDeleteMessage shouldBe true

                    verify {
                        buildNotificationService.buildTokenNotification(
                            accountId = AccountId(accountId),
                            mobilePhone = phoneNumber,
                            token = decryptedToken,
                        )
                    }

                    verify {
                        notificationService.notify(expectedNotification)
                    }

                    verify(exactly = 0) {
                        encryptService.decrypt(any())
                    }
                }
            }

            context("com código criptografado") {
                it("deve descriptografar o código antes de processar") {
                    // Given
                    val encryptedToken = "encrypted123456"
                    val decryptedToken = "123456"
                    val phoneNumber = "+*************"
                    val accountId = "ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"

                    val cognitoTokenMessage = CognitoTokenMessageTO(
                        code = encryptedToken,
                        decryptedCode = null,
                        source = "cognito",
                        username = "testuser",
                        sub = "sub-123",
                        accountId = accountId,
                        userPoolId = "us-east-1_ABC123",
                        phoneNumber = phoneNumber,
                        phoneNumberVerified = true,
                        email = "<EMAIL>",
                        emailVerified = true,
                        userStatus = "CONFIRMED",
                        timestamp = "2023-01-01T00:00:00Z",
                    )

                    val expectedNotification = ChatbotRawTemplatedNotification(
                        mobilePhone = phoneNumber,
                        accountId = AccountId(accountId),
                        clientId = mockk(),
                        configurationKey = mockk(),
                    )

                    every { encryptService.decrypt(encryptedToken) } returns decryptedToken
                    every {
                        buildNotificationService.buildTokenNotification(
                            accountId = AccountId(accountId),
                            mobilePhone = phoneNumber,
                            token = decryptedToken,
                        )
                    } returns expectedNotification

                    val message = buildMessage(cognitoTokenMessage)

                    // When
                    val response = handler.handleMessage(message)

                    // Then
                    response.shouldDeleteMessage shouldBe true

                    verify {
                        encryptService.decrypt(encryptedToken)
                    }

                    verify {
                        buildNotificationService.buildTokenNotification(
                            accountId = AccountId(accountId),
                            mobilePhone = phoneNumber,
                            token = decryptedToken,
                        )
                    }

                    verify {
                        notificationService.notify(expectedNotification)
                    }
                }
            }

            context("com dados mínimos obrigatórios") {
                it("deve processar mensagem com apenas campos obrigatórios") {
                    // Given
                    val decryptedToken = "123456"
                    val phoneNumber = "+*************"
                    val accountId = "ACCOUNT-460a346e-7e5d-4991-b001-9ae44a1df061"

                    val cognitoTokenMessage = CognitoTokenMessageTO(
                        code = null,
                        decryptedCode = decryptedToken,
                        source = null,
                        username = null,
                        sub = null,
                        accountId = accountId,
                        userPoolId = null,
                        phoneNumber = phoneNumber,
                        phoneNumberVerified = null,
                        email = null,
                        emailVerified = null,
                        userStatus = null,
                        timestamp = null,
                    )

                    val expectedNotification = ChatbotRawTemplatedNotification(
                        mobilePhone = phoneNumber,
                        accountId = AccountId(accountId),
                        clientId = mockk(),
                        configurationKey = mockk(),
                    )

                    every {
                        buildNotificationService.buildTokenNotification(
                            accountId = AccountId(accountId),
                            mobilePhone = phoneNumber,
                            token = decryptedToken,
                        )
                    } returns expectedNotification

                    val message = buildMessage(cognitoTokenMessage)

                    // When
                    val response = handler.handleMessage(message)

                    // Then
                    response.shouldDeleteMessage shouldBe true

                    verify {
                        buildNotificationService.buildTokenNotification(
                            accountId = AccountId(accountId),
                            mobilePhone = phoneNumber,
                            token = decryptedToken,
                        )
                    }

                    verify {
                        notificationService.notify(expectedNotification)
                    }
                }
            }
        }

        describe("quando ocorrer erro no processamento") {
            it("deve chamar handleError e não deletar a mensagem") {
                // Given
                val invalidJson = "invalid json"
                val message = Message.builder().body(invalidJson).build()

                // When
                val response = handler.handleError(message, RuntimeException("Test error"))

                // Then
                response.shouldDeleteMessage shouldBe false
            }
        }

        describe("funções de log") {
            it("deve mascarar o código no CognitoTokenMessageTO.toLog()") {
                // Given
                val originalCode = "*********"
                val cognitoTokenMessage = CognitoTokenMessageTO(
                    code = originalCode,
                    decryptedCode = null,
                    source = null,
                    username = null,
                    sub = null,
                    accountId = null,
                    userPoolId = null,
                    phoneNumber = null,
                    phoneNumberVerified = null,
                    email = null,
                    emailVerified = null,
                    userStatus = null,
                    timestamp = null,
                )

                // When
                val loggedMessage = cognitoTokenMessage.toLog()

                // Then
                loggedMessage.code shouldBe "12***89"
            }

            it("deve mascarar o código no CognitoToken.toLog()") {
                // Given
                val originalCode = "*********"
                val cognitoToken = CognitoToken(
                    code = originalCode,
                    user = UserId("+*************"),
                    accountId = AccountId("ACCOUNT-123"),
                )

                // When
                val loggedToken = cognitoToken.toLog()

                // Then
                loggedToken.code shouldBe "1***9"
            }
        }
    }

    private fun buildMessage(cognitoTokenMessage: CognitoTokenMessageTO): Message {
        val messageBody = getObjectMapper().writeValueAsString(cognitoTokenMessage)
        return Message.builder().body(messageBody).build()
    }
}

package ai.chatbot.adapters.openai

import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.TransactionService
import ai.chatbot.app.balance
import ai.chatbot.app.bill.PendingBillsService
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.conversation.Action
import ai.chatbot.app.conversation.ActionType
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.newActionExecutorLocator
import ai.chatbot.app.notification.FridayNotificationContextTemplatesService
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import arrow.core.right
import com.theokanning.openai.completion.chat.ChatMessage
import com.theokanning.openai.completion.chat.ChatMessageRole
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.verify

class OpenAIFunctionMessageParserAndExecutorIntegrationTest : DescribeSpec() {
    private val openAIFunctionMessageParser = OpenAIFunctionMessageParser()

    val notificationService = mockk<NotificationService>(relaxed = true)
    val paymentAdapter = mockk<PaymentAdapter>()
    val conversationHistoryService = mockk<ConversationHistoryService>(relaxed = true)
    val interactionWindowService = mockk<InteractionWindowService>()
    private val openAIAdapter = mockk<OpenAIAdapter>(relaxed = true)
    private val transactionService = mockk<TransactionService>(relaxed = true)
    private val pendingBillsService = mockk<PendingBillsService>(relaxed = true)

    private val locator =
        newActionExecutorLocator(
            notificationService = notificationService,
            paymentAdapter = paymentAdapter,
            conversationHistoryService = conversationHistoryService,
            transactionService = transactionService,
            interactionWindowService = interactionWindowService,
            openAIAdapter = openAIAdapter,
            pendingBillsService = pendingBillsService,
            notificationContextTemplatesService = FridayNotificationContextTemplatesService(),
        )

    val userId = UserId("UserId")
    val accountId = AccountId("accountId")
    val walletId = WalletId("walletId")
    private val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = UserId("*************"),
            name = "Nome",
            accountGroups = emptyList(),
            status = AccountStatus.ACTIVE,
        )

    val billComingDueHistoryState =
        BillComingDueHistoryState(
            walletWithBills =
            WalletWithBills(
                bills = listOf(billView, billView2, billView3),
                walletId = walletId,
                walletName = "nome da carteira",
            ),
            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = false, billComingDueNotifiedAt = null),
            balance = null,
            user =
            User(
                accountId = accountId,
                id = userId,
                name = "Nome do usuário",
                accountGroups = listOf(),
                status = AccountStatus.ACTIVE,
            ),
            contacts = emptyList(),
            subscription = null,
        )

    init {

        describe("quando processar uma resposta de função") {
            it("deve processar o sendMessage corretamente") {
                val payload =
                    buildFunctionMessagePayload().apply {
                        sendResponse = "Olá"
                    }
                val response = openAIFunctionMessageParser.processFunction(payload)
                executeAction(response.acoes.single())

                response.acoes.size shouldBe 1
                val action = response.acoes[0]
                action.shouldBeInstanceOf<Action.SendMessage>()
                action.content shouldBe "Olá"

                verify {
                    notificationService.notify(user.id, any(), "Olá")
                }
            }

            it("deve filtrar o sendMessage se houver outra ação") {
                val payload =
                    buildFunctionMessagePayload().apply {
                        sendResponse = "Olá"
                        pixTransaction = PixTransactionTO().apply {
                            amount = 10_00L
                            type = "CPF"
                            key = "12345678910"
                        }
                    }

                val response = openAIFunctionMessageParser.processFunction(payload)

                response.acoes shouldBe listOf(Action.PixTransaction(10_00L, key = "12345678910", type = "CPF", ignoreLastUsed = false))
            }

            it("não deve filtrar o sendMessage se a outra ação não bloquear resposta") {
                val payload =
                    buildFunctionMessagePayload().apply {
                        sendResponse = "Olá"
                        saveNotSupportedFeatureYet = "Gerar relatório"
                    }

                val response = openAIFunctionMessageParser.processFunction(payload)

                response.acoes.size shouldBe 2
                val msgAction = response.acoes.firstOrNull { it is Action.SendMessage }
                val featureRequestAction = response.acoes.firstOrNull { it is Action.NotSupportedFeature }

                msgAction.shouldBeInstanceOf<Action.SendMessage>()
                msgAction.content shouldBe "Olá"

                featureRequestAction.shouldBeInstanceOf<Action.NotSupportedFeature>()
                featureRequestAction.content shouldBe listOf("Gerar relatório")
            }

            it("deve processar o addFeatureRequest corretamente") {

                val payload =
                    buildFunctionMessagePayload().apply {
                        saveNotSupportedFeatureYet = "Olá"
                    }
                val response =
                    openAIFunctionMessageParser.processFunction(
                        payload,
                    )
                executeAction(response.acoes.single())

                response.acoes.size shouldBe 1
                val action = response.acoes[0]
                action.shouldBeInstanceOf<Action.NotSupportedFeature>()
                action.content shouldBe listOf("Olá")
            }

            it("deve processar o refreshBalanceAndForecasts corretamente") {
                coEvery {
                    paymentAdapter.getBalanceAndForecast(any(), any())
                } returns balance.right()

                val payload =
                    buildFunctionMessagePayload().apply {
                        refreshBalanceAndForecasts = true
                    }
                val response =
                    openAIFunctionMessageParser.processFunction(
                        payload,
                    )
                executeAction(response.acoes.single())

                response.acoes.size shouldBe 1
                response.acoes[0].shouldBeInstanceOf<Action.RefreshBalance>()

                coVerify {
                    paymentAdapter.getBalanceAndForecast(any(), any())
                }
            }
        }

        describe("Ao processar uma resposta de texto") {
            it("deve retornar a mensagem como uma action de texto") {
                val payload = buildChatMessageForMessage("Olá como texto")
                val response = openAIFunctionMessageParser.parseChatMessage(payload)
                executeAction(response.acoes.single())

                response.acoes.size shouldBe 1
                val action = response.acoes[0].shouldBeInstanceOf<Action.SendMessage>()
                action.content shouldBe "Olá como texto"

                verify {
                    notificationService.notify(user.id, any(), "Olá como texto")
                }
            }

            listOf(
                Pair("n&#227;o", "não"),
                Pair("voc&#234;", "você"),
                Pair("&#224;s", "às"),
                Pair("Ol\u00e1 como texto", "Olá como texto"),
                Pair(
                    "Ol\\u00e1, Fulano! Hoje n\\u00e3o h\\u00e1 contas vencidas pendentes para pagamento. Se precisar de mais alguma coisa, estou \\u00e0 disposi\\u00e7\\u00e3o!",
                    "Olá, Fulano! Hoje não há contas vencidas pendentes para pagamento. Se precisar de mais alguma coisa, estou à disposição!",
                ),
            ).forEach { (input, expected) ->
                it("deve retornar a mensagem sem caracteres unicode") {
                    val payload = buildChatMessageForMessage(input)
                    val response = openAIFunctionMessageParser.parseChatMessage(payload)
                    executeAction(response.acoes.single())

                    response.acoes.size shouldBe 1
                    val messageAction = response.acoes[0]
                    messageAction.shouldBeInstanceOf<Action.SendMessage>()
                    messageAction.name shouldBe ActionType.MSG
                    messageAction.content shouldBe expected

                    verify {
                        notificationService.notify(user.id, any(), expected)
                    }
                }
            }
        }
    }

    private fun buildFunctionMessagePayload(): FunctionMessagePayload =
        FunctionMessagePayload().apply {
            verificacaoDeIntegridade = ""
            entendimento = ""
        }

    private fun buildChatMessageForMessage(message: String): ChatMessage = ChatMessage(ChatMessageRole.ASSISTANT.value(), message)

    private fun executeAction(action: Action) {
        locator.locate(action.name).execute(
            action.toCommandExecutor(user),
        )
    }
}